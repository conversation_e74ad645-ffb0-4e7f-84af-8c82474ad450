# INFO 2025-08-07 12:44:04,593
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 12:44:18,927
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:32,913
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:37,133
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:41,348
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:46,023
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:50,208
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:54,343
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:34:58,670
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:02,873
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:07,157
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:11,360
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:15,612
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:19,948
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:24,075
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:28,900
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:33,087
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:37,236
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:35:46,029
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-07 15:35:58,744
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121993bf0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.279496
    headers: {'Date': 'Thu, 07 Aug 2025 07:35:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '4', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-07 15:36:01,694
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:06,129
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:10,454
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:14,601
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:18,863
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:23,376
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:27,825
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:32,018
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:38,624
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:36:45,095
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-07 15:36:55,843
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x12499d610>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.256898
    headers: {'Date': 'Thu, 07 Aug 2025 07:36:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'Retry-After': '7', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-07 15:37:01,248
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:05,405
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:09,652
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:14,120
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:18,253
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:22,504
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:26,784
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:31,128
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:35,226
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-07 15:37:39,602
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-07 15:37:50,069
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1249c28d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.263361
    headers: {'Date': 'Thu, 07 Aug 2025 07:37:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'Retry-After': '13', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-07 15:37:59,172
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1249c28d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.274043
    headers: {'Date': 'Thu, 07 Aug 2025 07:37:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '3', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-07 15:38:02,133
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.275628
    headers: {'Date': 'Thu, 07 Aug 2025 07:37:59 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'b2b4e7a3b4554353a3e7bbe012a8f0c7', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: 

# INFO 2025-08-07 15:38:02,133
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-07 15:54:15,685
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(anl15_gr_cal_fy3_total, bucket(rank(rsk70_mfm2_asetrd_midcap), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl14_high_capex_fy2, bucket(rank(oth455_competitor_roam_w5_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_12_m_st_dev, bucket(rank(oth455_competitor_roam_w1_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_cal_fy3_6m_chg, bucket(rank(rsk70_mfm2_asetrd_indadj), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_18_m_mktcap, bucket(rank(oth455_competitor_roam_w2_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl14_mean_ebitda_fy2, bucket(rank(rsk70_mfm2_asetrd_earnvar), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl14_median_roe_fy1, bucket(rank(rsk70_mfm2_asetrd_size), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl69_dvd_record_dt, bucket(rank(rsk70_mfm2_asetrd_profit), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(anl4_ebit_number, bucket(rank(rsk70_mfm2_asetrd_srisku), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl4_fcf_flag, bucket(rank(rsk70_mfm2_asetrd_size), range="0, 1, 0.1"))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.373366
    headers: {'Date': 'Thu, 07 Aug 2025 07:54:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '1a735946c8c54b49834b313badb25151', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-07 15:54:19,585
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-07 15:55:37,224
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_cal_fy3_6m_chg, bucket(rank(oth455_relation_roam_w1_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_s_cal_fy0_total, bucket(rank(oth455_competitor_roam_w3_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_12_m_6m_chg, bucket(rank(oth455_relation_roam_w5_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_cal_fy1_cos, bucket(rank(oth455_relation_roam_w5_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(anl4_netprofit_low, bucket(rank(oth455_competitor_roam_w5_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_cal_fy1_st_dev, bucket(rank(rsk70_mfm2_asetrd_season), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_cal_fy0_val, bucket(rank(oth455_relation_roam_w4_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl69_dvd_record_dt, bucket(rank(rsk70_mfm2_asetrd_srisk), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(anl69_nav_best_eeps_nxt_yr, bucket(rank(rsk70_mfm2_asetrd_indmom), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl69_qerf_dvd, bucket(rank(oth455_competitor_roam_w4_pca_fact3_value), range="0, 1, 0.1"))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.705182
    headers: {'Date': 'Thu, 07 Aug 2025 07:55:34 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '2be60ddd3004453e923faa78965ffb5d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-07 15:55:41,071
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 


INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions (29).txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m⚙️ 配置: 7Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions (29).txt[0m
INFO - [32m✅ 成功加载 13248 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  2. 2: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  3. 3: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  4. 4: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  5. 5: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m✅ 创建1325个任务 (编号1-1325)[0m
INFO - [32m🚀 开始渐进式启动 7 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1325 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/13248[0m
INFO - [32m🔧 槽位: 1/7 | 待处理: 1324 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
INFO - [32m🔧 槽位4 启动[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位6 启动[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 2/1325 (0.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 3.8 唯一Alpha/分钟 | 唯一提取: 10/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1318 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 8/1325 (0.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 40/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1315 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:13:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度10个任务 (起始0 + 会话10)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 12/1325 (0.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 60/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1313 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:31:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 16/1325 (1.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 80/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1311 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度20个任务 (起始0 + 会话20)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 24/1325 (1.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.5 唯一Alpha/分钟 | 唯一提取: 120/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1307 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:20:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度30个任务 (起始0 + 会话30)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 30/1325 (2.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.9 唯一Alpha/分钟 | 唯一提取: 150/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1304 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:34:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 36/1325 (2.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.2 唯一Alpha/分钟 | 唯一提取: 180/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1301 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:08:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度40个任务 (起始0 + 会话40)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 40/1325 (3.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 20.2 唯一Alpha/分钟 | 唯一提取: 200/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1299 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:53:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 44/1325 (3.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 220/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1297 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:59:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 46/1325 (3.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.4 唯一Alpha/分钟 | 唯一提取: 230/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1296 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:07:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 48/1325 (3.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.3 唯一Alpha/分钟 | 唯一提取: 240/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1295 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:09:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度50个任务 (起始0 + 会话50)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 52/1325 (3.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 260/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1293 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:04:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 54/1325 (4.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 270/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1292 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:06:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 56/1325 (4.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.1 唯一Alpha/分钟 | 唯一提取: 280/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1291 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 58/1325 (4.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.3 唯一Alpha/分钟 | 唯一提取: 290/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1290 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:28:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度60个任务 (起始0 + 会话60)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 64/1325 (4.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 320/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1287 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:06:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 66/1325 (5.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 330/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1286 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:08:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度70个任务 (起始0 + 会话70)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 70/1325 (5.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 350/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1284 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:05:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 72/1325 (5.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 360/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1283 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:07:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 74/1325 (5.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.8 唯一Alpha/分钟 | 唯一提取: 370/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1282 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:21:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度80个任务 (起始0 + 会话80)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 80/1325 (6.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 400/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1279 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:05:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 82/1325 (6.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.2 唯一Alpha/分钟 | 唯一提取: 410/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1278 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 86/1325 (6.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 430/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1276 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:10:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度90个任务 (起始0 + 会话90)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 92/1325 (6.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 20.0 唯一Alpha/分钟 | 唯一提取: 460/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1273 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:03:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 94/1325 (7.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.0 唯一Alpha/分钟 | 唯一提取: 470/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1272 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:21:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度100个任务 (起始0 + 会话100)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 104/1325 (7.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 520/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1267 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:08:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 106/1325 (8.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 530/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1266 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:11:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 108/1325 (8.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.2 唯一Alpha/分钟 | 唯一提取: 540/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1265 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度110个任务 (起始0 + 会话110)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 116/1325 (8.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 20.1 唯一Alpha/分钟 | 唯一提取: 580/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1261 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:04:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 118/1325 (8.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 19.2 唯一Alpha/分钟 | 唯一提取: 590/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1260 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:20:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度120个任务 (起始0 + 会话120)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
WARNING - [33m⏰ [槽位3] 任务48等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 126/1325 (9.5%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 20.0 唯一Alpha/分钟 | 唯一提取: 630/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1255 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:07:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 128/1325 (9.7%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 19.2 唯一Alpha/分钟 | 唯一提取: 640/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1254 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:22:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度130个任务 (起始0 + 会话130)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 134/1325 (10.1%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 670/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1251 | 失败: 1[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:11:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 138/1325 (10.4%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 690/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1249 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度140个任务 (起始0 + 会话140)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 140/1325 (10.6%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 19.4 唯一Alpha/分钟 | 唯一提取: 700/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1248 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 142/1325 (10.7%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 19.2 唯一Alpha/分钟 | 唯一提取: 710/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1247 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:23:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度150个任务 (起始0 + 会话150)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 150/1325 (11.3%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 750/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1243 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:11:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 154/1325 (11.6%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 770/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1241 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 158/1325 (11.9%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 790/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1239 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度160个任务 (起始0 + 会话160)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 162/1325 (12.2%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 810/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1237 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 166/1325 (12.5%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 830/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1235 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度170个任务 (起始0 + 会话170)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 170/1325 (12.8%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 850/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1233 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 174/1325 (13.1%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 870/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1231 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度180个任务 (起始0 + 会话180)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 180/1325 (13.6%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 20.1 唯一Alpha/分钟 | 唯一提取: 900/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1228 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:11:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 182/1325 (13.7%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 910/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1227 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:20:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 188/1325 (14.2%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 20.0 唯一Alpha/分钟 | 唯一提取: 940/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1224 | 失败: 1[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度190个任务 (起始0 + 会话190)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 192/1325 (14.5%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 20.2 唯一Alpha/分钟 | 唯一提取: 960/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1222 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:11:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 194/1325 (14.6%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 970/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1221 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:20:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 198/1325 (14.9%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 990/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1219 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:17:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度200个任务 (起始0 + 会话200)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 204/1325 (15.4%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 20.1 唯一Alpha/分钟 | 唯一提取: 1020/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1216 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 206/1325 (15.5%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 1030/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1215 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:18:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度210个任务 (起始0 + 会话210)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 212/1325 (16.0%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 20.1 唯一Alpha/分钟 | 唯一提取: 1060/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1212 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度220个任务 (起始0 + 会话220)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 220/1325 (16.6%) | 成功率: 99.5%[0m
INFO - [32m⚡ 唯一效率: 20.3 唯一Alpha/分钟 | 唯一提取: 1100/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1208 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 222/1325 (16.8%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.2 唯一Alpha/分钟 | 唯一提取: 1110/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1207 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 226/1325 (17.1%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.3 唯一Alpha/分钟 | 唯一提取: 1130/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1205 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度230个任务 (起始0 + 会话230)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 232/1325 (17.5%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.4 唯一Alpha/分钟 | 唯一提取: 1160/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1202 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 236/1325 (17.8%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.4 唯一Alpha/分钟 | 唯一提取: 1180/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1200 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:12:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 238/1325 (18.0%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.3 唯一Alpha/分钟 | 唯一提取: 1190/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1199 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度240个任务 (起始0 + 会话240)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 246/1325 (18.6%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.5 唯一Alpha/分钟 | 唯一提取: 1230/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1195 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:12:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 248/1325 (18.7%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.3 唯一Alpha/分钟 | 唯一提取: 1240/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1194 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度250个任务 (起始0 + 会话250)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 252/1325 (19.0%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.4 唯一Alpha/分钟 | 唯一提取: 1260/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1192 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 256/1325 (19.3%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.5 唯一Alpha/分钟 | 唯一提取: 1280/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1190 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度260个任务 (起始0 + 会话260)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 260/1325 (19.6%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.6 唯一Alpha/分钟 | 唯一提取: 1300/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1188 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:12:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 262/1325 (19.8%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.4 唯一Alpha/分钟 | 唯一提取: 1310/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1187 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 266/1325 (20.1%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.5 唯一Alpha/分钟 | 唯一提取: 1330/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1185 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度270个任务 (起始0 + 会话270)[0m
ERROR - [31m❌ [槽位0] 任务138状态检查异常: HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Read timed out. (read timeout=60)[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 272/1325 (20.5%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.4 唯一Alpha/分钟 | 唯一提取: 1360/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1182 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:18:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度280个任务 (起始0 + 会话280)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 280/1325 (21.1%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.7 唯一Alpha/分钟 | 唯一提取: 1400/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1178 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:12:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 282/1325 (21.3%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.7 唯一Alpha/分钟 | 唯一提取: 1410/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1177 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 286/1325 (21.6%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.6 唯一Alpha/分钟 | 唯一提取: 1430/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1175 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度290个任务 (起始0 + 会话290)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 292/1325 (22.0%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1460/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1172 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 294/1325 (22.2%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.7 唯一Alpha/分钟 | 唯一提取: 1470/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1171 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度300个任务 (起始0 + 会话300)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 300/1325 (22.6%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1500/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1168 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 306/1325 (23.1%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1530/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1165 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:12:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 308/1325 (23.2%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1540/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1164 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度310个任务 (起始0 + 会话310)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 314/1325 (23.7%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1570/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1161 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:14:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 318/1325 (24.0%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1590/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1159 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度320个任务 (起始0 + 会话320)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 320/1325 (24.2%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1600/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1158 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 322/1325 (24.3%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1610/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1157 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:17:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 328/1325 (24.8%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1640/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1154 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度330个任务 (起始0 + 会话330)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 330/1325 (24.9%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.7 唯一Alpha/分钟 | 唯一提取: 1650/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1153 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度340个任务 (起始0 + 会话340)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 340/1325 (25.7%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 1700/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1148 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:13:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 342/1325 (25.8%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 1710/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1147 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:15:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 344/1325 (26.0%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.7 唯一Alpha/分钟 | 唯一提取: 1720/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1146 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:21:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 348/1325 (26.3%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1740/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1144 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度350个任务 (起始0 + 会话350)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 354/1325 (26.7%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 1770/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1141 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:17:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 358/1325 (27.0%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1790/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1139 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度360个任务 (起始0 + 会话360)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 360/1325 (27.2%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1800/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1138 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:22:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 366/1325 (27.6%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 1830/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1135 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:19:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度370个任务 (起始0 + 会话370)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 370/1325 (27.9%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 1850/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1133 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:18:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 374/1325 (28.2%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1870/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1131 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:22:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 378/1325 (28.5%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1890/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1129 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:21:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度380个任务 (起始0 + 会话380)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 380/1325 (28.7%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1900/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1128 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:23:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 386/1325 (29.1%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 1930/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1125 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:22:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度390个任务 (起始0 + 会话390)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 392/1325 (29.6%) | 成功率: 99.7%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 1960/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1122 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:24:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度400个任务 (起始0 + 会话400)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 400/1325 (30.2%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2000/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1118 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:24:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 404/1325 (30.5%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2020/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1116 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:23:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 406/1325 (30.6%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2030/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1115 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:24:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度410个任务 (起始0 + 会话410)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 410/1325 (30.9%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2050/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1113 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:23:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 412/1325 (31.1%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2060/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1112 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:25:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 418/1325 (31.5%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2090/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1109 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:25:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度420个任务 (起始0 + 会话420)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 420/1325 (31.7%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2100/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1108 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:26:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 424/1325 (32.0%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2120/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1106 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:26:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 428/1325 (32.3%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2140/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1104 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:27:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度430个任务 (起始0 + 会话430)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 430/1325 (32.5%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2150/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1103 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:28:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 432/1325 (32.6%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 2160/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1102 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度440个任务 (起始0 + 会话440)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 440/1325 (33.2%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2200/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1098 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:27:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 444/1325 (33.5%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2220/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1096 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:27:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 446/1325 (33.7%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2230/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1095 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度450个任务 (起始0 + 会话450)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 452/1325 (34.1%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2260/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1092 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:30:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 456/1325 (34.4%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2280/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1090 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:29:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 458/1325 (34.6%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.9 唯一Alpha/分钟 | 唯一提取: 2290/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1089 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度460个任务 (起始0 + 会话460)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 462/1325 (34.9%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2310/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1087 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 466/1325 (35.2%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2330/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1085 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度470个任务 (起始0 + 会话470)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 472/1325 (35.6%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2360/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1082 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 476/1325 (35.9%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2380/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1080 | 失败: 1[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:30:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 478/1325 (36.1%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2390/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1079 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:32:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度480个任务 (起始0 + 会话480)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 482/1325 (36.4%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2410/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1077 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:32:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 488/1325 (36.8%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2440/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1074 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:31:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度490个任务 (起始0 + 会话490)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 490/1325 (37.0%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2450/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1073 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:32:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 494/1325 (37.3%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2470/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1071 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:32:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 498/1325 (37.6%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2490/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1069 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:32:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度500个任务 (起始0 + 会话500)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 502/1325 (37.9%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2510/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1067 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:32:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 504/1325 (38.0%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.1 唯一Alpha/分钟 | 唯一提取: 2520/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1066 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:33:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 506/1325 (38.2%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 21.0 唯一Alpha/分钟 | 唯一提取: 2530/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1065 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:34:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 508/1325 (38.3%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.8 唯一Alpha/分钟 | 唯一提取: 2540/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1064 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:38:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度510个任务 (起始0 + 会话510)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 512/1325 (38.6%) | 成功率: 99.8%[0m
INFO - [32m⚡ 唯一效率: 20.5 唯一Alpha/分钟 | 唯一提取: 2560/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1062 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:45:47[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位1] 任务250等待超时[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 516/1325 (38.9%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 20.4 唯一Alpha/分钟 | 唯一提取: 2580/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1059 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:46:47[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位3] 任务254等待超时[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 518/1325 (39.1%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 20.2 唯一Alpha/分钟 | 唯一提取: 2590/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1057 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:51:30[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位0] 任务257等待超时[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.350924
    headers: {'Date': 'Fri, 08 Aug 2025 03:29:58 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '34333a9fdfc341b29d849536945cb0f2', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度520个任务 (起始0 + 会话520)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 520/1325 (39.2%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 20.2 唯一Alpha/分钟 | 唯一提取: 2600/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1055 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:51:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 524/1325 (39.5%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 20.2 唯一Alpha/分钟 | 唯一提取: 2620/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1053 | 失败: 4[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:51:15[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.395342
    headers: {'Date': 'Fri, 08 Aug 2025 03:31:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '21496a7d8607486ebfac04d470dd9001', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.369032
    headers: {'Date': 'Fri, 08 Aug 2025 03:32:27 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '065a2296a599477abe317a80b7a146e9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位0] 任务270 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 524/1325 (39.5%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 20.0 唯一Alpha/分钟 | 唯一提取: 2620/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1052 | 失败: 5[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:55:20[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_rank(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_mean(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_mean(fnd28_value_08366, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.418718
    headers: {'Date': 'Fri, 08 Aug 2025 03:32:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8626d60aa6f74a5789c2cad28da225bf', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m⏰ [槽位5] 任务262等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 524/1325 (39.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 2620/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1051 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:58:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m✅ [槽位0] 任务274重试成功提交[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:01.046438
    headers: {'Date': 'Fri, 08 Aug 2025 03:34:07 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '22956bc1a8994766bd1158032d041249', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位1] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度530个任务 (起始0 + 会话530)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 530/1325 (40.0%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 2650/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1048 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:58:19[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.397234
    headers: {'Date': 'Fri, 08 Aug 2025 03:35:22 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '6c4a6c275b69439397acb9399b28b109', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位1] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 532/1325 (40.2%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.9 唯一Alpha/分钟 | 唯一提取: 2660/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1047 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:59:12[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位1] 任务277重试成功提交[0m
WARNING - [33m⏰ [槽位6] 任务264等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 532/1325 (40.2%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2660/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1046 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:02:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 538/1325 (40.6%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 2690/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1043 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:05:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度540个任务 (起始0 + 会话540)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 540/1325 (40.8%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 2700/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1042 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:05:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度550个任务 (起始0 + 会话550)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 550/1325 (41.5%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 2750/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1037 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:03:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 554/1325 (41.8%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2770/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1035 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:04:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 556/1325 (42.0%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2780/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1034 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:05:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度560个任务 (起始0 + 会话560)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 562/1325 (42.4%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 2810/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1031 | 失败: 7[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:03:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 564/1325 (42.6%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2820/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1030 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:05:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 566/1325 (42.7%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2830/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1029 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:06:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 568/1325 (42.9%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2840/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1028 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:07:26[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度570个任务 (起始0 + 会话570)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 574/1325 (43.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2870/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1025 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:06:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 576/1325 (43.5%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2880/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1024 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:07:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度580个任务 (起始0 + 会话580)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 580/1325 (43.8%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 2900/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1022 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:06:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 582/1325 (43.9%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 2910/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1021 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:09:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 584/1325 (44.1%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 2920/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1020 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:11:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 586/1325 (44.2%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 2930/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1019 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:11:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度590个任务 (起始0 + 会话590)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 592/1325 (44.7%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 2960/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1016 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:11:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 594/1325 (44.8%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 2970/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1015 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:12:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 598/1325 (45.1%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 2990/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1013 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:13:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度600个任务 (起始0 + 会话600)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 602/1325 (45.4%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3010/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1011 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:14:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 606/1325 (45.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3030/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1009 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:13:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 608/1325 (45.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3040/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1008 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:14:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度610个任务 (起始0 + 会话610)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 614/1325 (46.3%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3070/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1005 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:13:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 616/1325 (46.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3080/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1004 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:14:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 618/1325 (46.6%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3090/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1003 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:16:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度620个任务 (起始0 + 会话620)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 622/1325 (46.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3110/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1001 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:16:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 626/1325 (47.2%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3130/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 999 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:16:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 628/1325 (47.4%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3140/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 998 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:16:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度630个任务 (起始0 + 会话630)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 630/1325 (47.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3150/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 997 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:18:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 632/1325 (47.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3160/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 996 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:18:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 634/1325 (47.8%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3170/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 995 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:19:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 638/1325 (48.2%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3190/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 993 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:18:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度640个任务 (起始0 + 会话640)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 640/1325 (48.3%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3200/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 992 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:19:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 644/1325 (48.6%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3220/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 990 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:20:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 648/1325 (48.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3240/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 988 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:21:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度650个任务 (起始0 + 会话650)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 652/1325 (49.2%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3260/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 986 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:20:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 654/1325 (49.4%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3270/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 985 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:20:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 658/1325 (49.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3290/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 983 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:21:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度660个任务 (起始0 + 会话660)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 660/1325 (49.8%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.5 唯一Alpha/分钟 | 唯一提取: 3300/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 982 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 664/1325 (50.1%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3320/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 980 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:21:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 666/1325 (50.3%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3330/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 979 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 668/1325 (50.4%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3340/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 978 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度670个任务 (起始0 + 会话670)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 672/1325 (50.7%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3360/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 976 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 676/1325 (51.0%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3380/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 974 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 678/1325 (51.2%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3390/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 973 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度680个任务 (起始0 + 会话680)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 682/1325 (51.5%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3410/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 971 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 684/1325 (51.6%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3420/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 970 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:23:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度690个任务 (起始0 + 会话690)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 690/1325 (52.1%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3450/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 967 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:23:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 692/1325 (52.2%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3460/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 966 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:25:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度700个任务 (起始0 + 会话700)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 702/1325 (53.0%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3510/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 961 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:23:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 706/1325 (53.3%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3530/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 959 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:25:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度710个任务 (起始0 + 会话710)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 710/1325 (53.6%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3550/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 957 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:24:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 714/1325 (53.9%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 3570/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 955 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:24:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/1325 (54.2%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3590/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 953 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:24:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度720个任务 (起始0 + 会话720)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 722/1325 (54.5%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3610/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 951 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:26:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 724/1325 (54.6%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3620/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 950 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:26:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 726/1325 (54.8%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3630/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 949 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:27:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度730个任务 (起始0 + 会话730)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 730/1325 (55.1%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3650/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 947 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:26:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 734/1325 (55.4%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 3670/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 945 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:26:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 736/1325 (55.5%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.6 唯一Alpha/分钟 | 唯一提取: 3680/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 944 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:29:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度740个任务 (起始0 + 会话740)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 742/1325 (56.0%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3710/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 941 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:28:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 748/1325 (56.5%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 3740/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 938 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:27:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度750个任务 (起始0 + 会话750)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 750/1325 (56.6%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3750/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 937 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:29:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 754/1325 (56.9%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3770/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 935 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:29:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 758/1325 (57.2%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3790/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 933 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:30:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度760个任务 (起始0 + 会话760)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 762/1325 (57.5%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3810/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 931 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:30:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 764/1325 (57.7%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.7 唯一Alpha/分钟 | 唯一提取: 3820/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 930 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:31:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度770个任务 (起始0 + 会话770)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 770/1325 (58.1%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 3850/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 927 | 失败: 7[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:30:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 774/1325 (58.4%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 3870/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 925 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:31:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 778/1325 (58.7%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 19.8 唯一Alpha/分钟 | 唯一提取: 3890/13248[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 923 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:31:15[0m
INFO - [32m============================================================[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121a8c110>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.262506
    headers: {'Date': 'Fri, 08 Aug 2025 04:40:34 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '26', 'Retry-After': '26', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121afdd90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.264173
    headers: {'Date': 'Fri, 08 Aug 2025 04:41:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '11', 'Retry-After': '11', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121afdd90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.256051
    headers: {'Date': 'Fri, 08 Aug 2025 04:41:58 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '2', 'Retry-After': '2', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.258812
    headers: {'Date': 'Fri, 08 Aug 2025 04:42:01 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'cc04c75948184b688b5439813a7c1a70', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m

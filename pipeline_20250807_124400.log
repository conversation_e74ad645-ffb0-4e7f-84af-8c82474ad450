INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions (28).txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m🔄 断点续传: 从第1个任务开始[0m
INFO - [32m⚙️ 配置: 7Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions (28).txt[0m
INFO - [32m✅ 成功加载 5000 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: group_scale(anl15_gr_cal_fy0_pe, bucket(rank(oth45...[0m
INFO - [32m  2. 2: group_scale(anl69_ebit_best_eeps_cur_yr, bucket(ra...[0m
INFO - [32m  3. 3: group_zscore(anl15_s_ltg_st_dev, bucket(rank(rsk70...[0m
INFO - [32m  4. 4: group_scale(anl15_s_ltg_6m_chg, bucket(rank(oth455...[0m
INFO - [32m  5. 5: group_neutralize(anl69_eqy_dvd_sh_last, bucket(ran...[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m🔄 断点续传: 跳过前10个Alpha，剩余4990个[0m
INFO - [32m✅ 创建499个任务 (编号2-500)[0m
INFO - [32m🚀 开始渐进式启动 7 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/499 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/4990[0m
INFO - [32m🔧 槽位: 1/7 | 待处理: 498 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
INFO - [32m🔧 槽位4 启动[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位6 启动[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 2/499 (0.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 2.1 唯一Alpha/分钟 | 唯一提取: 10/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 492 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 6/499 (1.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 5.7 唯一Alpha/分钟 | 唯一提取: 30/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 490 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:01:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度11个任务 (起始1 + 会话10)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 12/499 (2.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 9.7 唯一Alpha/分钟 | 唯一提取: 60/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 487 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:01:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 14/499 (2.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.3 唯一Alpha/分钟 | 唯一提取: 70/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 486 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:46:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 16/499 (3.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 8.7 唯一Alpha/分钟 | 唯一提取: 80/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 485 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:32:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 18/499 (3.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 9.2 唯一Alpha/分钟 | 唯一提取: 90/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 484 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:18:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度21个任务 (起始1 + 会话20)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 22/499 (4.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 9.8 唯一Alpha/分钟 | 唯一提取: 110/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 482 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:01:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 28/499 (5.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.3 唯一Alpha/分钟 | 唯一提取: 140/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 479 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:50:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度31个任务 (起始1 + 会话30)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 30/499 (6.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 9.7 唯一Alpha/分钟 | 唯一提取: 150/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 478 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:06:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 34/499 (6.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.6 唯一Alpha/分钟 | 唯一提取: 170/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 476 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:45:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 36/499 (7.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.2 唯一Alpha/分钟 | 唯一提取: 180/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 475 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:54:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 38/499 (7.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.4 唯一Alpha/分钟 | 唯一提取: 190/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 474 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度41个任务 (起始1 + 会话40)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 42/499 (8.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.2 唯一Alpha/分钟 | 唯一提取: 210/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 472 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:33:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 44/499 (8.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.2 唯一Alpha/分钟 | 唯一提取: 220/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 471 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:33:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 46/499 (9.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.0 唯一Alpha/分钟 | 唯一提取: 230/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 470 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:37:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 48/499 (9.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.3 唯一Alpha/分钟 | 唯一提取: 240/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 469 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:54:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度51个任务 (起始1 + 会话50)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 52/499 (10.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.8 唯一Alpha/分钟 | 唯一提取: 260/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 467 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:44:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 54/499 (10.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.9 唯一Alpha/分钟 | 唯一提取: 270/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 466 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:42:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 58/499 (11.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.1 唯一Alpha/分钟 | 唯一提取: 290/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 464 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:39:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度61个任务 (起始1 + 会话60)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 62/499 (12.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.0 唯一Alpha/分钟 | 唯一提取: 310/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 462 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:41:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 68/499 (13.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 340/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 459 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:27:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度71个任务 (起始1 + 会话70)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 72/499 (14.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.3 唯一Alpha/分钟 | 唯一提取: 360/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 457 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:37:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 74/499 (14.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.3 唯一Alpha/分钟 | 唯一提取: 370/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 456 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:39:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 78/499 (15.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.4 唯一Alpha/分钟 | 唯一提取: 390/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 454 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:38:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度81个任务 (起始1 + 会话80)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 82/499 (16.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 410/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 452 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:34:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 84/499 (16.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 420/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 451 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:32:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 88/499 (17.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 440/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 449 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:33:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度91个任务 (起始1 + 会话90)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 90/499 (18.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 450/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 448 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:32:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 92/499 (18.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 460/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 447 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:34:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 96/499 (19.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 480/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 445 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:29:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度101个任务 (起始1 + 会话100)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 100/499 (20.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 500/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 443 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:32:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 104/499 (20.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 520/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 441 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:38:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 108/499 (21.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 540/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 439 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:34:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度111个任务 (起始1 + 会话110)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 112/499 (22.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 560/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 437 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:32:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 116/499 (23.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 580/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 435 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:35:06[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位6] 任务54等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 116/499 (23.2%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 580/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 434 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:39:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 118/499 (23.6%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 590/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 433 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:40:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度121个任务 (起始1 + 会话120)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 122/499 (24.4%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 610/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 431 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:36:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 124/499 (24.8%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 620/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 430 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:42:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度131个任务 (起始1 + 会话130)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 130/499 (26.1%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 650/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 427 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:35:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 132/499 (26.5%) | 成功率: 99.2%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 660/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 426 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:35:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 138/499 (27.7%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 690/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 423 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:38:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度141个任务 (起始1 + 会话140)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 142/499 (28.5%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 710/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 421 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:38:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 146/499 (29.3%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 730/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 419 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:35:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 148/499 (29.7%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 740/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 418 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:36:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度151个任务 (起始1 + 会话150)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 150/499 (30.1%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 750/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 417 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:38:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 152/499 (30.5%) | 成功率: 99.3%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 760/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 416 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:38:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 156/499 (31.3%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 780/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 414 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:41:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 158/499 (31.7%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 790/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 413 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:43:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度161个任务 (起始1 + 会话160)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 162/499 (32.5%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 810/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 411 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:40:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 166/499 (33.3%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 830/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 409 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:43:10[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位4] 任务82等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 166/499 (33.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 830/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 408 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:45:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 168/499 (33.7%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 840/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 407 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:47:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度171个任务 (起始1 + 会话170)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 170/499 (34.1%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 850/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 406 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:47:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 176/499 (35.3%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 880/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 403 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:43:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 178/499 (35.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 890/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 402 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:45:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度181个任务 (起始1 + 会话180)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 180/499 (36.1%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 900/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 401 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:46:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 182/499 (36.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 910/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 400 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:46:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 188/499 (37.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 940/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 397 | 失败: 2[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:45:08[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位3] 任务92等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 188/499 (37.7%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 940/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 396 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度191个任务 (起始1 + 会话190)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 190/499 (38.1%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 950/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 395 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:52:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 192/499 (38.5%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 960/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 394 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:52:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 196/499 (39.3%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 980/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 392 | 失败: 3[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 198/499 (39.7%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 990/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 391 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度201个任务 (起始1 + 会话200)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 200/499 (40.1%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 1000/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 390 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 202/499 (40.5%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 1010/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 389 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:51:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 204/499 (40.9%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 1020/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 388 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:51:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 208/499 (41.7%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 1040/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 386 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:50:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度211个任务 (起始1 + 会话210)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 210/499 (42.1%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 1050/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 385 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:52:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 216/499 (43.3%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1080/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 382 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:50:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 218/499 (43.7%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1090/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 381 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:50:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度221个任务 (起始1 + 会话220)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 220/499 (44.1%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1100/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 380 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:49:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 224/499 (44.9%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1120/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 378 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:51:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 226/499 (45.3%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1130/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 377 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:51:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 228/499 (45.7%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1140/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 376 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:51:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度231个任务 (起始1 + 会话230)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 232/499 (46.5%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1160/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 374 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:52:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 234/499 (46.9%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1170/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 373 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:52:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 236/499 (47.3%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1180/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 372 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度241个任务 (起始1 + 会话240)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 242/499 (48.5%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1210/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 369 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 244/499 (48.9%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1220/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 368 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 246/499 (49.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1230/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 367 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 248/499 (49.7%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1240/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 366 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度251个任务 (起始1 + 会话250)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 252/499 (50.5%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1260/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 364 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:51:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 256/499 (51.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1280/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 362 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:52:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 258/499 (51.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1290/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 361 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度261个任务 (起始1 + 会话260)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 260/499 (52.1%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1300/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 360 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:53:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 268/499 (53.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1340/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 356 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:54:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度271个任务 (起始1 + 会话270)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 270/499 (54.1%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1350/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 355 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:56:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 274/499 (54.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1370/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 353 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:56:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 276/499 (55.3%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1380/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 352 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:57:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度281个任务 (起始1 + 会话280)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 280/499 (56.1%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1400/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 350 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:00:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 282/499 (56.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1410/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 349 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:01:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
WARNING - [33m⏰ [槽位5] 任务143等待超时[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 286/499 (57.3%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1430/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 346 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:59:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 288/499 (57.7%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1440/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 345 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:00:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度291个任务 (起始1 + 会话290)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 290/499 (58.1%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1450/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 344 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:00:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 292/499 (58.5%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1460/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 343 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:02:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 296/499 (59.3%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1480/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 341 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:01:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度301个任务 (起始1 + 会话300)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 300/499 (60.1%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1500/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 339 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:00:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 304/499 (60.9%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1520/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 337 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:01:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 306/499 (61.3%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1530/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 336 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:01:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 308/499 (61.7%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1540/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 335 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:02:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度311个任务 (起始1 + 会话310)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 310/499 (62.1%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1550/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 334 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:03:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 314/499 (62.9%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1570/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 332 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:04:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 316/499 (63.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1580/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 331 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:04:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 318/499 (63.7%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1590/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 330 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:06:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度321个任务 (起始1 + 会话320)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 324/499 (64.9%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 1620/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 327 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:03:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 326/499 (65.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1630/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 326 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:09:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度331个任务 (起始1 + 会话330)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 330/499 (66.1%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1649/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 324 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:08:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 336/499 (67.3%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1679/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 321 | 失败: 4[0m
INFO - [32m📡 API频率: 2/5 (40.0%) | 🚀 智能并行: 3槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:06:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度341个任务 (起始1 + 会话340)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 340/499 (68.1%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1699/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 319 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:11:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 344/499 (68.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1719/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 317 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:10:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 346/499 (69.3%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1729/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 316 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:10:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 348/499 (69.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1739/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 315 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:11:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度351个任务 (起始1 + 会话350)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 350/499 (70.1%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1749/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 314 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:11:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 352/499 (70.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1759/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 313 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:13:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 354/499 (70.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 1769/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 312 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:13:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度361个任务 (起始1 + 会话360)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 362/499 (72.5%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1809/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 308 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:11:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 364/499 (72.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1819/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 307 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:12:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 366/499 (73.3%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1829/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 306 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:13:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 368/499 (73.7%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1839/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 305 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:13:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度371个任务 (起始1 + 会话370)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 370/499 (74.1%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1849/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 304 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:15:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 374/499 (74.9%) | 成功率: 98.9%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1869/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 302 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:14:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 378/499 (75.8%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 1889/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 300 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:13:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度381个任务 (起始1 + 会话380)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 380/499 (76.2%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1899/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 299 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:15:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 384/499 (77.0%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1919/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 297 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:16:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 386/499 (77.4%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1929/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 296 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:16:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度391个任务 (起始1 + 会话390)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 390/499 (78.2%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 1949/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 294 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:15:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 394/499 (79.0%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1969/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 292 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:18:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 396/499 (79.4%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1979/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 291 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:19:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 398/499 (79.8%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 1989/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 290 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:20:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度401个任务 (起始1 + 会话400)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 404/499 (81.0%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2019/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 287 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:21:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 406/499 (81.4%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2029/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 286 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:21:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 408/499 (81.8%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2039/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 285 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:21:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度411个任务 (起始1 + 会话410)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 410/499 (82.2%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2049/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 284 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:21:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 412/499 (82.6%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2059/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 283 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:22:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 414/499 (83.0%) | 成功率: 99.0%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 2069/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 282 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:24:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 418/499 (83.8%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2089/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 280 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:23:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度421个任务 (起始1 + 会话420)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 420/499 (84.2%) | 成功率: 99.1%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 2099/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 279 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:24:24[0m
INFO - [32m============================================================[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121993bf0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.279496
    headers: {'Date': 'Thu, 07 Aug 2025 07:35:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '4', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x12499d610>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.256898
    headers: {'Date': 'Thu, 07 Aug 2025 07:36:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'Retry-After': '7', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1249c28d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.263361
    headers: {'Date': 'Thu, 07 Aug 2025 07:37:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'Retry-After': '13', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1249c28d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.274043
    headers: {'Date': 'Thu, 07 Aug 2025 07:37:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '3', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.275628
    headers: {'Date': 'Thu, 07 Aug 2025 07:37:59 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'b2b4e7a3b4554353a3e7bbe012a8f0c7', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
WARNING - [33m⏰ [槽位3] 任务216等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 420/499 (84.2%) | 成功率: 98.8%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 2099/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 278 | 失败: 5[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:33:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度431个任务 (起始1 + 会话430)[0m
WARNING - [33m⏰ [槽位0] 任务217等待超时[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 432/499 (86.6%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 2159/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 271 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:33:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 434/499 (87.0%) | 成功率: 98.6%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 2169/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 270 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:34:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度441个任务 (起始1 + 会话440)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 440/499 (88.2%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 2199/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 267 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:34:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 442/499 (88.6%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 2209/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 266 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:34:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 446/499 (89.4%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 2228/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 264 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:36:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 448/499 (89.8%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 2238/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 263 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:38:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度451个任务 (起始1 + 会话450)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 450/499 (90.2%) | 成功率: 98.7%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 2248/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 262 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:39:49[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位0] 任务232等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 450/499 (90.2%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 2248/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 261 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:42:58[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位1] 任务233等待超时[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 452/499 (90.6%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 2258/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 259 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:42:50[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(anl15_gr_cal_fy3_total, bucket(rank(rsk70_mfm2_asetrd_midcap), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl14_high_capex_fy2, bucket(rank(oth455_competitor_roam_w5_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_12_m_st_dev, bucket(rank(oth455_competitor_roam_w1_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_cal_fy3_6m_chg, bucket(rank(rsk70_mfm2_asetrd_indadj), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_18_m_mktcap, bucket(rank(oth455_competitor_roam_w2_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl14_mean_ebitda_fy2, bucket(rank(rsk70_mfm2_asetrd_earnvar), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl14_median_roe_fy1, bucket(rank(rsk70_mfm2_asetrd_size), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl69_dvd_record_dt, bucket(rank(rsk70_mfm2_asetrd_profit), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(anl4_ebit_number, bucket(rank(rsk70_mfm2_asetrd_srisku), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl4_fcf_flag, bucket(rank(rsk70_mfm2_asetrd_size), range="0, 1, 0.1"))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.373366
    headers: {'Date': 'Thu, 07 Aug 2025 07:54:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '1a735946c8c54b49834b313badb25151', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m⏰ [槽位5] 任务234等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 452/499 (90.6%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2258/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 258 | 失败: 9[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:43:37[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位6] 任务242重试成功提交[0m
WARNING - [33m⏰ [槽位4] 任务236等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 452/499 (90.6%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2258/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 257 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:44:06[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_cal_fy3_6m_chg, bucket(rank(oth455_relation_roam_w1_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_s_cal_fy0_total, bucket(rank(oth455_competitor_roam_w3_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_12_m_6m_chg, bucket(rank(oth455_relation_roam_w5_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_cal_fy1_cos, bucket(rank(oth455_relation_roam_w5_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(anl4_netprofit_low, bucket(rank(oth455_competitor_roam_w5_pca_fact1_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_rank(anl15_gr_cal_fy1_st_dev, bucket(rank(rsk70_mfm2_asetrd_season), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl15_gr_cal_fy0_val, bucket(rank(oth455_relation_roam_w4_pca_fact2_value), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl69_dvd_record_dt, bucket(rank(rsk70_mfm2_asetrd_srisk), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(anl69_nav_best_eeps_nxt_yr, bucket(rank(rsk70_mfm2_asetrd_indmom), range="0, 1, 0.1"))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_scale(anl69_qerf_dvd, bucket(rank(oth455_competitor_roam_w4_pca_fact3_value), range="0, 1, 0.1"))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.705182
    headers: {'Date': 'Thu, 07 Aug 2025 07:55:34 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '2be60ddd3004453e923faa78965ffb5d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 454/499 (91.0%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2268/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 256 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:44:08[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位4] 任务244重试成功提交[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 456/499 (91.4%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2278/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 255 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:45:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 458/499 (91.8%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2288/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 254 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:45:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度461个任务 (起始1 + 会话460)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 460/499 (92.2%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2298/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 253 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:46:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 462/499 (92.6%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2308/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 252 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:48:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 464/499 (93.0%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2318/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 251 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:49:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 468/499 (93.8%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2338/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 249 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:50:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度471个任务 (起始1 + 会话470)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 474/499 (95.0%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2368/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 246 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:50:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 476/499 (95.4%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2378/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 245 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:51:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 478/499 (95.8%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2388/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 244 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:52:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度481个任务 (起始1 + 会话480)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 482/499 (96.6%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2408/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 242 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:53:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 486/499 (97.4%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2428/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 240 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:54:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 488/499 (97.8%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2438/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 239 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:54:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度491个任务 (起始1 + 会话490)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 490/499 (98.2%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2448/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 238 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:54:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
WARNING - [33m⏰ [槽位6] 任务253等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 492/499 (98.6%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2458/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 236 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:55:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 494/499 (99.0%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2468/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 235 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:55:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 496/499 (99.4%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2478/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 234 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:55:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 498/499 (99.8%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2488/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 233 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:56:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度501个任务 (起始1 + 会话500)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 500/499 (100.2%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2498/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 232 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:56:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 504/499 (101.0%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2518/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 230 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:56:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 506/499 (101.4%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2528/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 229 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:58:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 508/499 (101.8%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 2538/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 228 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:59:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度511个任务 (起始1 + 会话510)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 510/499 (102.2%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 2548/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 227 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:00:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 516/499 (103.4%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2578/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 224 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:00:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度521个任务 (起始1 + 会话520)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 520/499 (104.2%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 2598/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 222 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:59:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 522/499 (104.6%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2608/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 221 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:02:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 526/499 (105.4%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 2628/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 219 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:05:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度531个任务 (起始1 + 会话530)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 532/499 (106.6%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2658/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 216 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:04:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 534/499 (107.0%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2668/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 215 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:05:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 536/499 (107.4%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2678/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 214 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:05:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度541个任务 (起始1 + 会话540)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 540/499 (108.2%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2698/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 212 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:05:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 542/499 (108.6%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2708/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 211 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:06:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 546/499 (109.4%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2728/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 209 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:06:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 548/499 (109.8%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2738/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 208 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:06:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度551个任务 (起始1 + 会话550)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 550/499 (110.2%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2747/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 207 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:07:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 554/499 (111.0%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2767/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 205 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:07:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 556/499 (111.4%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2777/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 204 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:08:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 558/499 (111.8%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2787/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 203 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:09:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度561个任务 (起始1 + 会话560)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 560/499 (112.2%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2797/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 202 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:09:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 562/499 (112.6%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2807/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 201 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:09:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 564/499 (113.0%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2817/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 200 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:10:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 568/499 (113.8%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2837/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 198 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:10:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度571个任务 (起始1 + 会话570)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 570/499 (114.2%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2847/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 197 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:10:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 572/499 (114.6%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2857/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 196 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:12:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 576/499 (115.4%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2877/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 194 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:12:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 578/499 (115.8%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2887/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 193 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:14:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度581个任务 (起始1 + 会话580)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 582/499 (116.6%) | 成功率: 98.1%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2907/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 191 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:13:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 584/499 (117.0%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2917/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 190 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:14:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 586/499 (117.4%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2926/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 189 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:14:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 588/499 (117.8%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2936/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 188 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:16:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度591个任务 (起始1 + 会话590)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 590/499 (118.2%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2946/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 187 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:16:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 594/499 (119.0%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2966/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 185 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:16:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 596/499 (119.4%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2976/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 184 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:17:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 598/499 (119.8%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2986/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 183 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:18:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度601个任务 (起始1 + 会话600)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 600/499 (120.2%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 2996/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 182 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:18:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 604/499 (121.0%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3016/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 180 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:18:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 606/499 (121.4%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3026/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 179 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:20:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 608/499 (121.8%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3036/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 178 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:20:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度611个任务 (起始1 + 会话610)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 610/499 (122.2%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3046/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 177 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:21:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 614/499 (123.0%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3066/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 175 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:21:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 618/499 (123.8%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3086/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 173 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:22:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度621个任务 (起始1 + 会话620)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 620/499 (124.2%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3096/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 172 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:23:26[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 622/499 (124.6%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3106/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 171 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:24:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 624/499 (125.1%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3116/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 170 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:24:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 628/499 (125.9%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3136/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 168 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:24:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度631个任务 (起始1 + 会话630)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 632/499 (126.7%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3156/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 166 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:24:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 634/499 (127.1%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3166/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 165 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:26:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 636/499 (127.5%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3176/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 164 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:27:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度641个任务 (起始1 + 会话640)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 642/499 (128.7%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3206/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 161 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:26:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 646/499 (129.5%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3226/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 159 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:28:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 648/499 (129.9%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3236/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 158 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:28:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度651个任务 (起始1 + 会话650)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 652/499 (130.7%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3256/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 156 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:31:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 656/499 (131.5%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3276/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 154 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:30:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 658/499 (131.9%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3286/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 153 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:31:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度661个任务 (起始1 + 会话660)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 660/499 (132.3%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3296/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 152 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:32:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 664/499 (133.1%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3316/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 150 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:32:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 666/499 (133.5%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3326/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 149 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:32:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 668/499 (133.9%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 3336/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 148 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:33:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度671个任务 (起始1 + 会话670)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 670/499 (134.3%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3346/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 147 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:35:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 674/499 (135.1%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3366/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 145 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:36:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度681个任务 (起始1 + 会话680)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 680/499 (136.3%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3396/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 142 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:39:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 684/499 (137.1%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3416/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 140 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:38:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度691个任务 (起始1 + 会话690)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 690/499 (138.3%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3446/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 137 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:39:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 694/499 (139.1%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3466/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 135 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:40:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 698/499 (139.9%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3486/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 133 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:41:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度701个任务 (起始1 + 会话700)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 700/499 (140.3%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3496/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 132 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:42:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 704/499 (141.1%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3516/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 130 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:42:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 708/499 (141.9%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3536/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 128 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:44:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度711个任务 (起始1 + 会话710)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 710/499 (142.3%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3546/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 127 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:45:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 714/499 (143.1%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3566/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 125 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:45:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 716/499 (143.5%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 3576/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 124 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:46:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 98.5%[0m
INFO - [32m⚡ 唯一效率: 11.5 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 123 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:48:33[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', BrokenPipeError(32, 'Broken pipe'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务370状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务372状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
WARNING - [33m⏰ [槽位2] 任务370等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 98.4%[0m
INFO - [32m⚡ 唯一效率: 11.5 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 122 | 失败: 12[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:49:02[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', BrokenPipeError(32, 'Broken pipe'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务379提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务379将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务379提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务379将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务372状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务379提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务380提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务380将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务380提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务380将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务380提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务372状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务381提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务381将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务381提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务381将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务381提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 98.0%[0m
INFO - [32m⚡ 唯一效率: 11.5 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 119 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:48:26[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位2] 任务382提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务382将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务372状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务382提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务382将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务382提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务383提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务383将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务372状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务383提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务383将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务383提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务384提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务384将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务384提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务384将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m⏰ [槽位6] 任务372等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 11.5 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 116 | 失败: 18[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:47:47[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位6] 任务385提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务385将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务384提交异常: ('Connection aborted.', BrokenPipeError(32, 'Broken pipe'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务386提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务386将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务385提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务385将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务386提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务386将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务385提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务387提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务387将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务386提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务388提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务388将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务387提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务387将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务388提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务388将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务387提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位6] 任务389提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务389将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务388提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务390提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务390将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务389提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务389将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务390提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务390将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务389提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 96.8%[0m
INFO - [32m⚡ 唯一效率: 11.4 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 110 | 失败: 24[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:45:53[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位6] 任务391提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务391将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务390提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位2] 任务392提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务392将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务391提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务391将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务392提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务392将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务391提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务393提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务393将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务392提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务394提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务394将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务393提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务393将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务394提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务394将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务393提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务395提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务395将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务394提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务396提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务396将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务395提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务395将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务396提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务396将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务395提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 11.4 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 104 | 失败: 30[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:43:57[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位6] 任务397提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务397将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务396提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务398提交异常: ('Connection aborted.', BrokenPipeError(32, 'Broken pipe'))[0m
WARNING - [33m🔄 [槽位2] 任务398将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务397提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务397将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务398提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务398将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务397提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务399提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务399将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务398提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务400提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务400将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务399提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务399将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务400提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务400将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务399提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务401提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务401将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务400提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务402提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务402将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务401提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务401将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务402提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务402将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务401提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 95.2%[0m
INFO - [32m⚡ 唯一效率: 11.4 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 98 | 失败: 36[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:42:01[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务403提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务403将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务402提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务404提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务404将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务403提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务403将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务404提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务404将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务403提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务405提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务405将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务404提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务406提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务406将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务405提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务405将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务406提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务406将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务405提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务407提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务407将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务406提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务408提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务408将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务407提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务407将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务408提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务408将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务407提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 94.5%[0m
INFO - [32m⚡ 唯一效率: 11.4 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 92 | 失败: 42[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:40:05[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位6] 任务409提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务409将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务408提交异常: ('Connection aborted.', BrokenPipeError(32, 'Broken pipe'))[0m
ERROR - [31m❌ [槽位2] 任务410提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务410将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务409提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务409将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务410提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务410将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务409提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务411提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务411将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务410提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务412提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务412将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务411提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务411将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务412提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务412将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务411提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务413提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务413将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务412提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务414提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务414将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位6] 任务413提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务413将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务414提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位2] 任务414将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务413提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 93.7%[0m
INFO - [32m⚡ 唯一效率: 11.3 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 86 | 失败: 48[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:38:08[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位3] 任务374状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务415提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务415将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务414提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务375状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务416提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务416将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务415提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务415将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务416提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务416将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务415提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务417提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位6] 任务417将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务416提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m⏰ [槽位3] 任务374等待超时[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务418提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务418将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务419提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位3] 任务419将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务378状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
WARNING - [33m⏰ [槽位1] 任务375等待超时[0m
ERROR - [31m❌ [槽位6] 任务417提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务417将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务376状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位1] 任务420提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位1] 任务420将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务418提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务418将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务419提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位3] 任务419将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务417提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务420提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位1] 任务420将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务421提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务421将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务418提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位3] 任务419提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务422提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位2] 任务422将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 任务423提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位3] 任务423将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位3] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务420提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/499 (143.9%) | 成功率: 92.6%[0m
INFO - [32m⚡ 唯一效率: 11.3 唯一Alpha/分钟 | 唯一提取: 3586/4990[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 77 | 失败: 57[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:34:45[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位6] 任务421提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位6] 任务421将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位6] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位0] 任务377状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m

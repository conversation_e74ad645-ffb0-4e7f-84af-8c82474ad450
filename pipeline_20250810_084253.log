INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions-7.txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m🔄 断点续传: 从第1074个任务开始[0m
INFO - [32m⚙️ 配置: 7Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions-7.txt[0m
INFO - [32m✅ 成功加载 21504 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: group_neutralize(power(ts_zscore(abs(rsk70_mfm2_us...[0m
INFO - [32m  2. 2: group_neutralize(power(ts_zscore(abs(rsk70_mfm2_us...[0m
INFO - [32m  3. 3: group_neutralize(power(ts_zscore(abs(rsk70_mfm2_us...[0m
INFO - [32m  4. 4: group_neutralize(power(ts_zscore(abs(rsk70_mfm2_us...[0m
INFO - [32m  5. 5: group_neutralize(power(ts_zscore(abs(rsk70_mfm2_us...[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m🔄 断点续传: 跳过前10740个Alpha，剩余10764个[0m
INFO - [32m✅ 创建1077个任务 (编号1075-2151)[0m
INFO - [32m🚀 开始渐进式启动 7 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1077 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/10764[0m
INFO - [32m🔧 槽位: 1/7 | 待处理: 1076 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_zscore(power(ts_rank(abs(rsk70_mfm2_usfast_value),252),2)-power(ts_rank(rsk70_mfm2_usfast_value,252),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_zscore(power(ts_rank(abs(rsk70_mfm2_usfast_value),252),2)-power(ts_rank(rsk70_mfm2_usfast_value,252),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.321508
    headers: {'Date': 'Sun, 10 Aug 2025 00:43:56 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e7794c12d34043e4a98f2c7ade754def', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),industry)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.384340
    headers: {'Date': 'Sun, 10 Aug 2025 00:44:08 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '9b9e7d3de345404499a05b4f11733ff3', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位1] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔧 槽位4 启动[0m
INFO - [32m🔧 槽位5 启动[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),20),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,20),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),20),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,20),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),20),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,20),2),exchange)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.487572
    headers: {'Date': 'Sun, 10 Aug 2025 00:44:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '08135a6e36ef41628c3ceb79de63ae4c', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位5] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1304e4e90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.304974
    headers: {'Date': 'Sun, 10 Aug 2025 00:44:41 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '19', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '19', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔧 槽位6 启动[0m
INFO - [32m✅ [槽位3] 任务1076重试成功提交[0m
INFO - [32m✅ [槽位1] 任务1077重试成功提交[0m
INFO - [32m✅ [槽位5] 任务1079重试成功提交[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 2/1077 (0.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1070 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 4/1077 (0.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1069 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 8/1077 (0.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1067 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:32:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1084个任务 (起始1074 + 会话10)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 14/1077 (1.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1064 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:23:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 16/1077 (1.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 1.3 唯一Alpha/分钟 | 唯一提取: 10/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1063 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:29:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 18/1077 (1.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 1.1 唯一Alpha/分钟 | 唯一提取: 10/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1062 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:57:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1094个任务 (起始1074 + 会话20)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 20/1077 (1.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 1.0 唯一Alpha/分钟 | 唯一提取: 10/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1061 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:01:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 26/1077 (2.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.9 唯一Alpha/分钟 | 唯一提取: 10/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1058 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:22:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 28/1077 (2.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.8 唯一Alpha/分钟 | 唯一提取: 10/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1057 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:25:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1104个任务 (起始1074 + 会话30)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 30/1077 (2.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 1.5 唯一Alpha/分钟 | 唯一提取: 20/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1056 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:48:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 34/1077 (3.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 2.9 唯一Alpha/分钟 | 唯一提取: 40/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1054 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 16:12:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 38/1077 (3.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 4.0 唯一Alpha/分钟 | 唯一提取: 60/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1052 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:50:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1114个任务 (起始1074 + 会话40)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 44/1077 (4.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 5.4 唯一Alpha/分钟 | 唯一提取: 90/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1049 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:36:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 48/1077 (4.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 6.4 唯一Alpha/分钟 | 唯一提取: 110/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1047 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:16:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1124个任务 (起始1074 + 会话50)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 50/1077 (4.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 6.7 唯一Alpha/分钟 | 唯一提取: 120/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1046 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:17:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 56/1077 (5.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 8.0 唯一Alpha/分钟 | 唯一提取: 150/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1043 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:52:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 58/1077 (5.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 7.7 唯一Alpha/分钟 | 唯一提取: 160/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1042 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:18:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1134个任务 (起始1074 + 会话60)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 62/1077 (5.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 8.3 唯一Alpha/分钟 | 唯一提取: 180/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1040 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 15:10:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1144个任务 (起始1074 + 会话70)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 70/1077 (6.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 9.7 唯一Alpha/分钟 | 唯一提取: 220/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1036 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:40:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 72/1077 (6.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 9.5 唯一Alpha/分钟 | 唯一提取: 230/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1035 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:54:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 76/1077 (7.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.0 唯一Alpha/分钟 | 唯一提取: 250/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1033 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:49:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1154个任务 (起始1074 + 会话80)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 82/1077 (7.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.8 唯一Alpha/分钟 | 唯一提取: 280/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1030 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:34:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 86/1077 (8.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.8 唯一Alpha/分钟 | 唯一提取: 300/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1028 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:44:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1164个任务 (起始1074 + 会话90)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 90/1077 (8.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.1 唯一Alpha/分钟 | 唯一提取: 320/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1026 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:39:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 96/1077 (8.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 350/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1023 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:27:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1174个任务 (起始1074 + 会话100)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 100/1077 (9.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.7 唯一Alpha/分钟 | 唯一提取: 370/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1021 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:38:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 102/1077 (9.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 11.8 唯一Alpha/分钟 | 唯一提取: 380/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1020 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:38:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 108/1077 (10.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 410/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1017 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:26:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1184个任务 (起始1074 + 会话110)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 114/1077 (10.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 440/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1014 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:31:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1194个任务 (起始1074 + 会话120)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 120/1077 (11.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.0 唯一Alpha/分钟 | 唯一提取: 470/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1011 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:23:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 124/1077 (11.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 12.9 唯一Alpha/分钟 | 唯一提取: 490/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1009 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:30:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 128/1077 (11.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.1 唯一Alpha/分钟 | 唯一提取: 510/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1007 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:28:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1204个任务 (起始1074 + 会话130)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 132/1077 (12.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.3 唯一Alpha/分钟 | 唯一提取: 530/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1005 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:25:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 136/1077 (12.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.3 唯一Alpha/分钟 | 唯一提取: 550/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1003 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:30:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1214个任务 (起始1074 + 会话140)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 142/1077 (13.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.4 唯一Alpha/分钟 | 唯一提取: 580/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 1000 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:31:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 144/1077 (13.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.4 唯一Alpha/分钟 | 唯一提取: 590/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 999 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:32:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 146/1077 (13.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.4 唯一Alpha/分钟 | 唯一提取: 600/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 998 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:34:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1224个任务 (起始1074 + 会话150)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 152/1077 (14.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.8 唯一Alpha/分钟 | 唯一提取: 630/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 995 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:29:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 154/1077 (14.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.7 唯一Alpha/分钟 | 唯一提取: 640/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 994 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:31:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 156/1077 (14.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.7 唯一Alpha/分钟 | 唯一提取: 650/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 993 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:32:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 158/1077 (14.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 13.8 唯一Alpha/分钟 | 唯一提取: 660/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 992 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:32:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1234个任务 (起始1074 + 会话160)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 164/1077 (15.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 690/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 989 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:24:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 168/1077 (15.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 710/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 987 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:25:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1244个任务 (起始1074 + 会话170)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 172/1077 (16.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 730/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 985 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:25:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 174/1077 (16.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 740/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 984 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:26:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1254个任务 (起始1074 + 会话180)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 180/1077 (16.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 770/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 981 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:20:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 182/1077 (16.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 780/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 980 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:25:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 186/1077 (17.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 800/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 978 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:30:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 188/1077 (17.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 810/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 977 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:32:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1264个任务 (起始1074 + 会话190)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 194/1077 (18.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 840/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 974 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:27:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 196/1077 (18.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 850/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 973 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:32:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 198/1077 (18.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 860/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 972 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:35:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1274个任务 (起始1074 + 会话200)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 204/1077 (18.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 890/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 969 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:36:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 206/1077 (19.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 900/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 968 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:36:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 208/1077 (19.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 910/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 967 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:39:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1284个任务 (起始1074 + 会话210)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 214/1077 (19.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 940/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 964 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:37:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 218/1077 (20.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 960/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 962 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:39:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1294个任务 (起始1074 + 会话220)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 220/1077 (20.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 970/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 961 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:42:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 226/1077 (21.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 1000/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 958 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:40:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 228/1077 (21.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 1010/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 957 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:42:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1304个任务 (起始1074 + 会话230)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 230/1077 (21.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 1020/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 956 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:43:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 234/1077 (21.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 1040/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 954 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:45:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
ERROR - [31m❌ [槽位1] 任务1192状态检查异常: HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Read timed out. (read timeout=60)[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务1193状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务1198状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务1197状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1199提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1199将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位0] 任务1195状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务1196状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1199提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1199将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务1199提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 236/1077 (21.9%) | 成功率: 99.6%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 1050/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 952 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:50:20[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位4] 任务1200提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1200将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务1192状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务1193状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务1198状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务1197状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务1195状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务1196状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1200提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位4] 任务1200将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务1200提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务1201提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1201将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务1192状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务1193状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务1198状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务1197状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务1195状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1201提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1201将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务1196状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1201提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务1202提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1202将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位1] 任务1192状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1202提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1202将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位2] 任务1193状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务1198状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务1197状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务1195状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务1196状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1202提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 236/1077 (21.9%) | 成功率: 98.3%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 1050/10764[0m
INFO - [32m🔧 槽位: 6/7 | 待处理: 949 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:52:46[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位4] 任务1203提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1203将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 任务1203提交异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
WARNING - [33m🔄 [槽位4] 任务1203将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务1192状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务1193状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务1198状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务1197状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务1195状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1203提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位6] 任务1196状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1204提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1204将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
ERROR - [31m❌ [槽位4] 任务1204提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1204将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位1] 任务1192状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1204提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位2] 任务1193状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位3] 任务1198状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1205提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1205将在3秒后重试 (1/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位5] 任务1197状态检查异常: ('Connection aborted.', OSError(22, 'Invalid argument'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位0] 任务1195状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位6] 任务1196状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位4] 任务1205提交异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
WARNING - [33m🔄 [槽位4] 任务1205将在3秒后重试 (2/3)[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
ERROR - [31m❌ 创建WQB会话异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
ERROR - [31m❌ [槽位4] 自动重登录失败: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m

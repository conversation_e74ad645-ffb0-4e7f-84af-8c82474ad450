import time
import requests


def submit_alpha(s, alpha_id):
    submit_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}/submit"

    # 第一轮提交
    while True:
        res = s.post(submit_url)
        if res.status_code == 201:
            print(f"Alpha {alpha_id} POST Status 201. Start submitting...")
            break

    # 第二轮提交
    count = 0
    while True:
        res = s.get(submit_url)
        if res.status_code == 200:
            retry = res.headers.get('Retry-After', 0)
            if retry:
                count += 1
                time.sleep(float(retry))
                if count % 5 == 0:
                    print(f"Alpha {alpha_id} GET Status 200. Waiting... {count}.")
            else:
                print(f"Alpha {alpha_id} was submitted successfully.")
                break
        elif res.status_code == 403:
            print(f"Alpha {alpha_id} GET Status {res.status_code}.")
            print(f"Alpha {alpha_id} submit failed. Need Improvement.")
            break
        elif res.status_code == 404:
            print(f"Alpha {alpha_id} GET Status {res.status_code}.")
            print(f"Alpha {alpha_id} submit failed. Time Out.")
            break
        else:
            print(f"Alpha {alpha_id} GET Status {res.status_code}.")
            print(f"Alpha {alpha_id} submit failed. Time Out.")
            break


if __name__ == '__main__':
    username = "<EMAIL>"
    password = "Kyz417442"
    s = requests.Session()
    s.auth = (username, password)
    response = s.post('https://api.worldquantbrain.com/authentication')

    # 这里面替换你的alpha_id
    submittable_alphas = ['VLV6mA5']
    for alpha_id in submittable_alphas:
        submit_alpha(s, alpha_id)
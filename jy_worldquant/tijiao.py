import requests
import json
import time
import os
import platform
import getpass
import sys
import logging
from requests.exceptions import RequestException

# 配置日志，替代部分print，便于控制输出
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


def input_with_asterisks(prompt):
    """输入密码时显示星号（仅Windows支持，其他系统使用getpass兼容）"""
    # 判断操作系统，非Windows直接使用getpass（无星号但跨平台）
    if platform.system() != "Windows":
        logger.debug("非Windows系统，使用getpass获取密码")
        return getpass.getpass(prompt)

    # Windows系统使用msvcrt实现星号显示
    try:
        import msvcrt  # 仅Windows可用
    except ImportError:
        logger.warning("Windows系统下msvcrt模块不可用，将使用明文输入")
        return input(prompt)

    print(prompt, end='', flush=True)
    password = []
    try:
        while True:
            char = msvcrt.getch()
            # 处理回车
            if char in [b'\r', b'\n']:
                print()
                break
            # 处理退格
            elif char == b'\x08':
                if password:
                    password.pop()
                    print('\b \b', end='', flush=True)
            # 处理Ctrl+C
            elif char == b'\x03':
                print()
                raise KeyboardInterrupt
            # 处理可打印ASCII字符
            elif 32 <= ord(char) <= 126:
                password.append(char.decode('ascii'))
                print('*', end='', flush=True)
            # 忽略非ASCII字符（避免多字节编码问题）
            else:
                continue
    except Exception as e:
        logger.error(f"密码输入错误: {e}，将使用明文输入")
        return input("请输入密码（明文显示）: ")
    return ''.join(password)


def login(account_choice=None):
    """登录函数，增加超时和会话有效性检查"""
    s = requests.Session()
    s.headers.update({"User-Agent": "AlphaSubmitter/1.0"})  # 添加用户代理

    logger.info("\n=== WorldQuant Brain 登录 ===")
    email = input("请输入邮箱: ").strip()
    if not email:
        logger.error("❌ 邮箱不能为空")
        return None

    # 获取密码（兼容跨平台）
    try:
        password = input_with_asterisks("请输入密码: ")
        if not password:
            logger.error("❌ 密码不能为空")
            return None
    except KeyboardInterrupt:
        logger.info("\n用户中断输入")
        return None
    except Exception as e:
        logger.error(f"密码获取失败: {e}")
        return None

    s.auth = (email, password)
    try:
        # 添加超时（10秒），避免无限等待
        response = s.post(
            'https://api.worldquantbrain.com/authentication',
            timeout=10
        )
        logger.debug(f"登录响应状态码: {response.status_code}")

        if response.status_code == 200:
            logger.info("✅ 登录成功")
            return s
        else:
            logger.error(f"❌ 登录失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text[:200]}")  # 显示部分内容
            return None
    except RequestException as e:
        logger.error(f"❌ 登录请求失败: {str(e)}")
        return None


def check_alpha_exists(s, alpha_id):
    """检查alpha是否存在，增加超时和格式验证"""
    # 简单验证alpha_id格式（假设为数字，可根据实际调整）
    if not alpha_id.isdigit():
        logger.error(f"❌ Alpha ID格式无效: {alpha_id}（应为数字）")
        return False, None

    try:
        response = s.get(
            f"https://api.worldquantbrain.com/alphas/{alpha_id}",
            timeout=10
        )
        if response.status_code == 200:
            try:
                alpha_data = response.json()
                logger.info(f"✅ Alpha {alpha_id} 存在（类型: {alpha_data.get('type', '未知')}）")
                return True, alpha_data
            except json.JSONDecodeError:
                logger.error(f"❌ Alpha {alpha_id} 响应格式错误（非JSON）")
                return False, None
        elif response.status_code == 404:
            logger.error(f"❌ Alpha {alpha_id} 不存在（404）")
            return False, None
        elif response.status_code == 401:
            logger.error("❌ 会话已过期，请重新登录")
            return False, None  # 触发重新登录
        else:
            logger.warning(f"⚠️ 检查Alpha状态异常，状态码: {response.status_code}")
            return False, None
    except RequestException as e:
        logger.error(f"❌ 检查Alpha {alpha_id} 失败: {str(e)}")
        return False, None


def get_alpha_recordsets(s, alpha_id):
    """获取alpha记录集，优化错误处理"""
    try:
        response = s.get(
            f"https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets",
            timeout=10
        )
        if response.status_code == 200:
            try:
                recordsets_data = response.json()
                count = recordsets_data.get('count', 0)
                logger.info(f"📊 Alpha {alpha_id} 有 {count} 个记录集")
                return recordsets_data
            except json.JSONDecodeError:
                logger.error(f"❌ 记录集响应格式错误（非JSON）")
                return None
        else:
            logger.warning(f"⚠️ 获取记录集失败，状态码: {response.status_code}")
            return None
    except RequestException as e:
        logger.error(f"❌ 获取记录集出错: {str(e)}")
        return None


def submit(s, alpha_id):
    """提交alpha，修复重试逻辑（使用POST而非GET）"""
    def submit_inner(s, alpha_id):
        """内部提交函数，处理限流和会话过期"""
        try:
            response = s.post(
                f"https://api.worldquantbrain.com/alphas/{alpha_id}/submit",
                timeout=10
            )
            logger.debug(f"提交响应状态码: {response.status_code}")

            # 处理限流（Retry-After）
            if response.status_code == 429 and "Retry-After" in response.headers:
                wait_time = int(response.headers["Retry-After"])
                logger.info(f"⚠️ 触发限流，将等待 {wait_time} 秒后重试")
                time.sleep(wait_time)
                return submit_inner(s, alpha_id)  # 递归重试（仍用POST）

            # 处理会话过期（401）
            if response.status_code == 401:
                logger.error("❌ 会话已过期，尝试重新登录")
                new_session = login()
                if new_session:
                    return submit_inner(new_session, alpha_id)
                else:
                    return None

            return response
        except RequestException as e:
            logger.error(f"❌ 提交连接错误: {str(e)}，10秒后重试")
            time.sleep(10)
            return submit_inner(s, alpha_id)  # 网络错误重试

    attempt = 1
    max_attempts = 5  # 限制最大重试次数（避免无限循环）
    while attempt <= max_attempts:
        logger.info(f"📤 提交尝试 {attempt}/{max_attempts} - Alpha {alpha_id}")
        result = submit_inner(s, alpha_id)
        
        if result is None:
            logger.error("❌ 提交失败，无法建立有效会话")
            return None
        
        if result.status_code == 200:
            return result
        
        logger.warning(f"⚠️ 提交尝试 {attempt} 失败，状态码: {result.status_code}")
        attempt += 1
        if attempt <= max_attempts:
            time.sleep(30)  # 重试间隔30秒（而非原2分钟，更灵活）

    logger.error(f"❌ 超过最大重试次数（{max_attempts}次），提交失败")
    return None


def submit_alpha(alpha_id, session=None):
    """提交alpha主函数，优化状态判断"""
    if not session:
        s = login()
        if not s:
            return False
    else:
        s = session

    # 先检查alpha是否存在
    exists, _ = check_alpha_exists(s, alpha_id)
    if not exists:
        return False

    # 执行提交
    res = submit(s, alpha_id)
    if not res:
        return False

    # 解析响应
    try:
        res_json = res.json()
    except json.JSONDecodeError:
        logger.error(f"❌ 提交响应非JSON格式: {res.text[:200]}")
        return False

    # 判断提交结果
    if res.status_code == 200:
        # 检查是否已提交
        if 'is' in res_json and 'checks' in res_json['is']:
            for check in res_json['is']['checks']:
                if check['name'] == 'ALREADY_SUBMITTED':
                    logger.info(f"ℹ️ Alpha {alpha_id} 已提交过")
                    return True  # 已提交也算"成功"（避免重复操作）
                if check['result'] == 'FAIL':
                    logger.error(f"❌ 提交检查失败 - {check['name']}: {check['value']}（限制: {check['limit']}）")
                    return False
        logger.info(f"✅ Alpha {alpha_id} 提交成功！")
        return True
    else:
        logger.error(f"❌ 提交失败，响应信息: {json.dumps(res_json, indent=2)[:500]}")
        return False


def main():
    """主函数，优化用户交互"""
    logger.info("=== WorldQuant Brain Alpha 提交工具 ===")
    logger.info("功能: 提交Alpha、查询Alpha信息、重新登录")
    logger.info("输入 'quit' 退出，'relogin' 重新登录，'info <alpha_id>' 查询信息\n")

    # 初始化会话
    session = login()
    if not session:
        logger.error("❌ 登录失败，程序退出")
        return

    while True:
        user_input = input("\n请输入Alpha ID（或命令）: ").strip()
        if not user_input:
            logger.warning("⚠️ 输入不能为空，请重新输入")
            continue

        # 处理命令
        if user_input.lower() == 'quit':
            logger.info("👋 程序退出，再见！")
            break
        elif user_input.lower() == 'relogin':
            logger.info("🔄 重新登录中...")
            session = login()
            if not session:
                logger.error("❌ 重新登录失败，程序退出")
                break
            continue
        elif user_input.lower().startswith('info '):
            alpha_id = user_input[5:].strip()
            if not alpha_id:
                logger.warning("⚠️ 请指定要查询的Alpha ID（格式: info <alpha_id>）")
                continue
            logger.info(f"\n===== Alpha {alpha_id} 信息 =====")
            exists, alpha_data = check_alpha_exists(session, alpha_id)
            if exists:
                get_alpha_recordsets(session, alpha_id)
            logger.info("===================================")
            continue
        else:
            # 视为Alpha ID，执行提交
            alpha_id = user_input
            logger.info(f"\n===== 开始处理 Alpha {alpha_id} =====")
            success = submit_alpha(alpha_id, session)
            logger.info(f"===== Alpha {alpha_id} 处理结束 =====")
            if not success:
                logger.warning("建议检查Alpha ID或网络后重试")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\n👋 用户中断程序，退出")
    except Exception as e:
        logger.critical(f"❌ 程序发生未捕获错误: {str(e)}", exc_info=True)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
特定Alpha优化器
专门处理指定alpha ID的二阶和三阶优化
"""

import os
import sys
import logging
import configparser
from datetime import datetime
from typing import List, Tuple, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import AlphaService, SimulationService, SubmissionService
from common_config import CommonConfig
from common_auth import BrainAuth
from db_utils import setup_database


class SpecificAlphaOptimizer:
    """特定Alpha优化器"""
    
    def __init__(self, config_file: str = 'config.ini'):
        """
        初始化优化器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = None
        self.config_parser = None
        self.common_config = None
        self.db_service = None
        self.auth_service = None
        
        # 服务对象
        self.alpha_service = None
        self.simulation_service = None
        self.submission_service = None
        
    def initialize(self) -> bool:
        """
        初始化系统组件
        
        Returns:
            是否初始化成功
        """
        try:
            # 1. 设置日志系统
            self._setup_logging()
            
            # 2. 加载配置
            self._load_configuration()
            
            # 3. 初始化数据库
            self._setup_database()
            
            # 4. 初始化认证服务
            self._setup_auth_service()
            
            # 5. 初始化业务服务
            self._setup_services()
            
            self.logger.info("特定Alpha优化器初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"特定Alpha优化器初始化失败: {str(e)}")
            else:
                print(f"特定Alpha优化器初始化失败: {str(e)}")
            return False
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志文件名
        log_file = os.path.join(
            log_dir, 
            f"specific_alpha_optimizer_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
        )
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def _load_configuration(self):
        """加载配置"""
        self.config_parser = configparser.ConfigParser()
        self.config_parser.read(self.config_file, encoding='utf-8')
        self.common_config = CommonConfig.from_config(self.config_parser)
        self.logger.info("配置加载完成")
    
    def _setup_database(self):
        """设置数据库"""
        self.db_service = setup_database(self.config_parser)
        self.logger.info("数据库初始化完成")
    
    def _setup_auth_service(self):
        """设置认证服务"""
        self.auth_service = BrainAuth()
        self.logger.info("认证服务初始化完成")
    
    def _setup_services(self):
        """设置业务服务"""
        self.alpha_service = AlphaService(self.common_config, self.auth_service, self.db_service)
        self.simulation_service = SimulationService(self.common_config, self.auth_service, self.db_service)
        self.submission_service = SubmissionService(self.common_config, self.auth_service, self.db_service)
        self.logger.info("业务服务初始化完成")
    
    def get_alpha_expression_by_id(self, alpha_id: str) -> Optional[Tuple[str, int]]:
        """
        根据alpha ID获取表达式和decay
        
        Args:
            alpha_id: Alpha ID
            
        Returns:
            (expression, decay) 元组，如果失败返回None
        """
        try:
            session = self.auth_service.login()
            url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
            resp = session.get(url)
            
            if resp.status_code == 200:
                data = resp.json()
                expression = data['regular']['code']
                decay = data['settings']['decay']
                self.logger.info(f"获取Alpha {alpha_id} 成功: {expression}, decay: {decay}")
                return (expression, decay)
            else:
                self.logger.error(f"获取Alpha {alpha_id} 失败: {resp.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取Alpha {alpha_id} 异常: {str(e)}")
            return None
    
    def optimize_specific_alphas(self, alpha_ids: List[str], run_second_order: bool = True, 
                                run_third_order: bool = True) -> bool:
        """
        优化特定的Alpha列表
        
        Args:
            alpha_ids: Alpha ID列表
            run_second_order: 是否运行二阶优化
            run_third_order: 是否运行三阶优化
            
        Returns:
            是否成功
        """
        self.logger.info(f"开始优化特定Alpha: {alpha_ids}")
        
        try:
            # 获取Alpha表达式
            alpha_expressions = []
            for alpha_id in alpha_ids:
                result = self.get_alpha_expression_by_id(alpha_id)
                if result:
                    alpha_expressions.append(result)
                else:
                    self.logger.warning(f"跳过无法获取的Alpha: {alpha_id}")
            
            if not alpha_expressions:
                self.logger.error("没有成功获取任何Alpha表达式")
                return False
            
            self.logger.info(f"成功获取 {len(alpha_expressions)} 个Alpha表达式")
            
            # 运行二阶优化
            if run_second_order:
                self._run_second_order_optimization(alpha_expressions)
            
            # 运行三阶优化
            if run_third_order:
                self._run_third_order_optimization(alpha_expressions)
            
            return True
            
        except Exception as e:
            self.logger.error(f"优化特定Alpha失败: {str(e)}")
            return False
    
    def _run_second_order_optimization(self, alpha_expressions: List[Tuple[str, int]]):
        """
        运行二阶优化
        
        Args:
            alpha_expressions: Alpha表达式列表
        """
        self.logger.info("开始二阶优化")
        
        try:
            # 生成二阶Alpha
            second_order_alphas = []
            for expr, decay in alpha_expressions:
                # 检查是否已包含group操作
                if not any(op in expr for op in ["group_neutralize", "group_rank", "group_zscore", 
                                               "group_mean", "group_extra", "group_backfill", 
                                               "group_scale", "group_cartesian_product"]):
                    factory_alphas = self.alpha_service.alpha_factory.create_group_operations(
                        "group_neutralize", expr
                    )
                    for factory_alpha in factory_alphas:
                        second_order_alphas.append((factory_alpha, decay))
                else:
                    self.logger.info(f"Alpha已包含group操作，跳过: {expr}")
            
            if not second_order_alphas:
                self.logger.warning("没有生成二阶Alpha")
                return
            
            # 过滤新Alpha
            second_order_alphas = self.alpha_service.filter_new_alphas(second_order_alphas)
            
            if second_order_alphas:
                self.logger.info(f"生成 {len(second_order_alphas)} 个二阶Alpha，开始仿真")
                
                # 仿真二阶Alpha
                for progress in self.simulation_service.batch_simulate(second_order_alphas):
                    self.logger.info(f"二阶Alpha仿真进度: {progress}%")
                        
                self.logger.info(f"二阶优化完成，处理了 {len(second_order_alphas)} 个Alpha")
            else:
                self.logger.info("没有新的二阶Alpha需要处理")
                
        except Exception as e:
            self.logger.error(f"二阶优化失败: {str(e)}")
    
    def _run_third_order_optimization(self, alpha_expressions: List[Tuple[str, int]]):
        """
        运行三阶优化
        
        Args:
            alpha_expressions: Alpha表达式列表
        """
        self.logger.info("开始三阶优化")
        
        try:
            # 生成三阶Alpha
            third_order_alphas = []
            for expr, decay in alpha_expressions:
                # 检查是否已包含trade_when操作
                if "trade_when" not in expr:
                    factory_alphas = self.alpha_service.alpha_factory.create_trade_when_operations(
                        expr, self.common_config.events
                    )
                    for factory_alpha in factory_alphas:
                        third_order_alphas.append((factory_alpha, decay))
                else:
                    self.logger.info(f"Alpha已包含trade_when操作，跳过: {expr}")
            
            if not third_order_alphas:
                self.logger.warning("没有生成三阶Alpha")
                return
            
            # 过滤新Alpha
            third_order_alphas = self.alpha_service.filter_new_alphas(third_order_alphas)
            
            if third_order_alphas:
                self.logger.info(f"生成 {len(third_order_alphas)} 个三阶Alpha，开始仿真")
                
                # 仿真三阶Alpha
                for progress in self.simulation_service.batch_simulate(third_order_alphas):
                    self.logger.info(f"三阶Alpha仿真进度: {progress}%")
                        
                self.logger.info(f"三阶优化完成，处理了 {len(third_order_alphas)} 个Alpha")
            else:
                self.logger.info("没有新的三阶Alpha需要处理")
                
        except Exception as e:
            self.logger.error(f"三阶优化失败: {str(e)}")


def main():
    """主函数"""
    # 目标Alpha ID列表
    target_alpha_ids = ["voQR35v", "92njMv1", "PAV0Kxq", "voWMxWA"]
    
    print("=== 特定Alpha优化器 ===")
    print(f"目标Alpha IDs: {target_alpha_ids}")
    print(f"区域: USA 1 TOP3000")
    print("优化级别: 二阶和三阶优化")
    print("=" * 50)
    
    # 创建优化器实例
    optimizer = SpecificAlphaOptimizer()
    
    # 初始化
    if not optimizer.initialize():
        print("❌ 优化器初始化失败")
        return False
    
    # 运行优化
    success = optimizer.optimize_specific_alphas(
        alpha_ids=target_alpha_ids,
        run_second_order=True,
        run_third_order=True
    )
    
    if success:
        print("✅ 特定Alpha优化完成")
    else:
        print("❌ 特定Alpha优化失败")
    
    return success


if __name__ == "__main__":
    main()

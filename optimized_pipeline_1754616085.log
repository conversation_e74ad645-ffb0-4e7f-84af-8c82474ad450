# INFO 2025-08-08 09:21:29,432
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 09:21:34,574
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 10:28:14,062
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 11:30:01,094
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.350924
    headers: {'Date': 'Fri, 08 Aug 2025 03:29:58 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '34333a9fdfc341b29d849536945cb0f2', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-08 11:30:05,095
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 11:31:15,429
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.395342
    headers: {'Date': 'Fri, 08 Aug 2025 03:31:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '21496a7d8607486ebfac04d470dd9001', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-08 11:31:19,402
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 11:32:29,847
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_rank(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_n2v_p50_q200_w1_kmeans_cluster_20), ts_mean(fnd28_value_05480, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.369032
    headers: {'Date': 'Fri, 08 Aug 2025 03:32:27 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '065a2296a599477abe317a80b7a146e9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-08 11:32:40,187
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_mean(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_mean(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_scale(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_scale(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_zscore(fnd28_value_05480, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_05480, oth455_relation_roam_w5_pca_fact3_cluster_20), ts_zscore(fnd28_value_05480, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_rank(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_mean(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10), ts_mean(fnd28_value_08366, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.418718
    headers: {'Date': 'Fri, 08 Aug 2025 03:32:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8626d60aa6f74a5789c2cad28da225bf', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-08 11:32:48,602
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 11:34:09,693
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:01.046438
    headers: {'Date': 'Fri, 08 Aug 2025 03:34:07 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '22956bc1a8994766bd1158032d041249', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-08 11:34:13,833
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 11:35:24,884
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_rank(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_mean(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_scale(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_10), ts_zscore(fnd28_value_08366, 504))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 252))'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'EUR', 'universe': 'TOP2500', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'min(group_rank(fnd28_value_08366, oth455_competitor_n2v_p10_q200_w1_pca_fact1_cluster_20), ts_rank(fnd28_value_08366, 504))'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.397234
    headers: {'Date': 'Fri, 08 Aug 2025 03:35:22 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '6c4a6c275b69439397acb9399b28b109', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-08 11:35:29,011
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:11,605
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:16,261
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:21,312
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:26,005
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:30,836
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:35,139
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:41,862
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:46,738
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:50,978
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:39:59,763
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:04,845
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:09,129
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:13,406
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:17,855
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:22,160
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:26,375
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 12:40:36,817
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121a8c110>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.262506
    headers: {'Date': 'Fri, 08 Aug 2025 04:40:34 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '26', 'Retry-After': '26', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-08 12:40:39,702
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:43,977
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:49,092
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:40:53,650
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:00,859
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:05,305
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:09,702
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:15,093
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:19,639
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:23,948
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:28,198
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:32,483
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:36,914
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:41:41,328
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-08 12:41:51,726
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121afdd90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.264173
    headers: {'Date': 'Fri, 08 Aug 2025 04:41:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '11', 'Retry-After': '11', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-08 12:42:00,863
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x121afdd90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.256051
    headers: {'Date': 'Fri, 08 Aug 2025 04:41:58 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '2', 'Retry-After': '2', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-08 12:42:03,493
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.258812
    headers: {'Date': 'Fri, 08 Aug 2025 04:42:01 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'cc04c75948184b688b5439813a7c1a70', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: 

# INFO 2025-08-08 12:42:03,494
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:07,734
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:11,956
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:16,350
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:21,558
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:25,910
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:30,184
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:34,469
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-08 12:42:38,722
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 


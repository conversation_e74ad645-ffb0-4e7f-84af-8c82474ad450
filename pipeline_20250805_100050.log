INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s', '50.1s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions (27).txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m🔄 断点续传: 从第255个任务开始[0m
INFO - [32m⚙️ 配置: 8Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions (27).txt[0m
INFO - [32m✅ 成功加载 19940 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: group_neutralize(cashflow_dividends/cap, subindust...[0m
INFO - [32m  2. 2: group_neutralize(cashflow_dividends/cap, market)[0m
INFO - [32m  3. 3: group_neutralize(cashflow_dividends/cap, industry)[0m
INFO - [32m  4. 4: group_neutralize(cashflow_dividends/cap, sector)[0m
INFO - [32m  5. 5: group_neutralize(cashflow_dividends/cap, country)[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m🔄 断点续传: 跳过前2550个Alpha，剩余17390个[0m
INFO - [32m✅ 创建1739个任务 (编号256-1994)[0m
INFO - [32m🚀 开始渐进式启动 8 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位7 等待 50.1 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1739 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/17390[0m
INFO - [32m🔧 槽位: 2/8 | 待处理: 1737 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
INFO - [32m🔧 槽位4 启动[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位7 启动[0m
INFO - [32m🔧 槽位6 启动[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 2/1739 (0.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1731 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 6/1739 (0.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 4.9 唯一Alpha/分钟 | 唯一提取: 20/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1729 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 05:40:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度265个任务 (起始255 + 会话10)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 16/1739 (0.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.6 唯一Alpha/分钟 | 唯一提取: 70/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1724 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 22:01:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 18/1739 (1.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 10.4 唯一Alpha/分钟 | 唯一提取: 80/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1723 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 22:28:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度275个任务 (起始255 + 会话20)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 28/1739 (1.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 130/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1718 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:21:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度285个任务 (起始255 + 会话30)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 32/1739 (1.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 15.2 唯一Alpha/分钟 | 唯一提取: 150/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1716 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:01:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 34/1739 (2.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 160/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1715 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:26:04[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度295个任务 (起始255 + 会话40)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 42/1739 (2.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.5 唯一Alpha/分钟 | 唯一提取: 200/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1711 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:27:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 44/1739 (2.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 15.8 唯一Alpha/分钟 | 唯一提取: 210/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1710 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:50:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 46/1739 (2.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 15.7 唯一Alpha/分钟 | 唯一提取: 220/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1709 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:56:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度305个任务 (起始255 + 会话50)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 50/1739 (2.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 15.8 唯一Alpha/分钟 | 唯一提取: 240/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1707 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:54:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 54/1739 (3.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.5 唯一Alpha/分钟 | 唯一提取: 260/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1705 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:33:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 56/1739 (3.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.2 唯一Alpha/分钟 | 唯一提取: 270/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1704 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:44:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度315个任务 (起始255 + 会话60)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 60/1739 (3.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.8 唯一Alpha/分钟 | 唯一提取: 290/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1702 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:28:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 66/1739 (3.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.4 唯一Alpha/分钟 | 唯一提取: 320/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1699 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:14:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 68/1739 (3.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.6 唯一Alpha/分钟 | 唯一提取: 330/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1698 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:38:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度325个任务 (起始255 + 会话70)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 74/1739 (4.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.5 唯一Alpha/分钟 | 唯一提取: 360/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1695 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:13:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 76/1739 (4.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.5 唯一Alpha/分钟 | 唯一提取: 370/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1694 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:14:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 78/1739 (4.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.4 唯一Alpha/分钟 | 唯一提取: 380/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1693 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:15:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度335个任务 (起始255 + 会话80)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 82/1739 (4.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.0 唯一Alpha/分钟 | 唯一提取: 400/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1691 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:29:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 88/1739 (5.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.8 唯一Alpha/分钟 | 唯一提取: 430/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1688 | 失败: 0[0m
INFO - [32m📡 API频率: 2/5 (40.0%) | 🚀 智能并行: 3槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:07:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度345个任务 (起始255 + 会话90)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 92/1739 (5.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 450/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1686 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:13:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 96/1739 (5.5%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 470/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1684 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:12:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度355个任务 (起始255 + 会话100)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 100/1739 (5.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.8 唯一Alpha/分钟 | 唯一提取: 490/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1682 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:11:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 106/1739 (6.1%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 520/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1679 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:15:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 108/1739 (6.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 530/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1678 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:15:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度365个任务 (起始255 + 会话110)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 110/1739 (6.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.6 唯一Alpha/分钟 | 唯一提取: 540/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1677 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:18:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 118/1739 (6.8%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.6 唯一Alpha/分钟 | 唯一提取: 580/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1673 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:55:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度375个任务 (起始255 + 会话120)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 122/1739 (7.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.0 唯一Alpha/分钟 | 唯一提取: 600/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1671 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:11:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 126/1739 (7.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.1 唯一Alpha/分钟 | 唯一提取: 620/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1669 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:08:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 128/1739 (7.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.1 唯一Alpha/分钟 | 唯一提取: 630/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1668 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:09:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度385个任务 (起始255 + 会话130)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 134/1739 (7.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 18.5 唯一Alpha/分钟 | 唯一提取: 660/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1665 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 17:59:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 138/1739 (7.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 680/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1663 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:21:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度395个任务 (起始255 + 会话140)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 142/1739 (8.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 700/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1661 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:24:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度405个任务 (起始255 + 会话150)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 150/1739 (8.6%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.9 唯一Alpha/分钟 | 唯一提取: 740/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1657 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:19:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 152/1739 (8.7%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.9 唯一Alpha/分钟 | 唯一提取: 750/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1656 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:20:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 154/1739 (8.9%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.7 唯一Alpha/分钟 | 唯一提取: 760/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1655 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:24:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 156/1739 (9.0%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 17.2 唯一Alpha/分钟 | 唯一提取: 770/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1654 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:40:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度415个任务 (起始255 + 会话160)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 160/1739 (9.2%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.9 唯一Alpha/分钟 | 唯一提取: 790/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1652 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 18:50:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 162/1739 (9.3%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.6 唯一Alpha/分钟 | 唯一提取: 800/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1651 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:01:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 164/1739 (9.4%) | 成功率: 100.0%[0m
INFO - [32m⚡ 唯一效率: 16.4 唯一Alpha/分钟 | 唯一提取: 810/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1650 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:06:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
WARNING - [33m⏰ [槽位7] 任务334等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 166/1739 (9.5%) | 成功率: 99.4%[0m
INFO - [32m⚡ 唯一效率: 16.4 唯一Alpha/分钟 | 唯一提取: 820/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1648 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:06:00[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.320495
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:01 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '320a4bdd6a8d46e98876edbc8ec0041b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位7] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m⏰ [槽位0] 任务335等待超时[0m
WARNING - [33m⏰ [槽位6] 任务336等待超时[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.332523
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:15 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8eec7c79ea024a7698066ff6c7e5e99c', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.332193
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:27 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '90ecfd24530d48c0872696d9ef78f6f6', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f18290>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.309572
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:37 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '23', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '23', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f18290>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.251525
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:46 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '14', 'Retry-After': '14', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f18290>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.253280
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'Retry-After': '4', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.262870
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:49 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '510603b95b3348a0a75b5fe0c0621988', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31m❌ 创建WQB会话异常: WQB认证失败[0m
ERROR - [31m[槽位6] 自动重登录失败[0m
INFO - [32m✅ [槽位7] 任务347重试成功提交[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.359679
    headers: {'Date': 'Tue, 05 Aug 2025 02:52:16 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8962d009691043a0bd1da8b5e8e502f9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位0] 任务348重试成功提交[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 168/1739 (9.7%) | 成功率: 98.2%[0m
INFO - [32m⚡ 唯一效率: 16.1 唯一Alpha/分钟 | 唯一提取: 830/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1645 | 失败: 3[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:18:43[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.337614
    headers: {'Date': 'Tue, 05 Aug 2025 02:52:49 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'bd394fe6831d4ece9674b27b8f9c24e0', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f319a0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.266637
    headers: {'Date': 'Tue, 05 Aug 2025 02:52:59 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '1', 'Retry-After': '1', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度425个任务 (起始255 + 会话170)[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.591297
    headers: {'Date': 'Tue, 05 Aug 2025 02:53:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'cb166c7d737244ee89fb23dcb2a60946', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位6] 任务349 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 170/1739 (9.8%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 16.0 唯一Alpha/分钟 | 唯一提取: 840/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1643 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:21:09[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.357831
    headers: {'Date': 'Tue, 05 Aug 2025 02:53:40 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '36c85e35575e44bcb0abe829efdefd19', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 172/1739 (9.9%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 16.0 唯一Alpha/分钟 | 唯一提取: 850/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1642 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:21:13[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.352498
    headers: {'Date': 'Tue, 05 Aug 2025 02:54:16 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e952317f86ac44e2961172e796764659', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位6] 任务352重试成功提交[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 174/1739 (10.0%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.9 唯一Alpha/分钟 | 唯一提取: 860/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1641 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:23:42[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.352398
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'be9b199ecd0d48e0957bb26d1b5bfe75', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.373734
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '9c3b0d3c53134dcb89535dd0c42df6db', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位4] 任务350 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 174/1739 (10.0%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 15.8 唯一Alpha/分钟 | 唯一提取: 860/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1640 | 失败: 5[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:29:31[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.416866
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:44 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'b4c1ebfec3854c1ba7c1961ec425a69d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f1b410>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.326966
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:55 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '5', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '5', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 176/1739 (10.1%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 15.8 唯一Alpha/分钟 | 唯一提取: 870/17390[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1639 | 失败: 5[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:29:09[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.585886
    headers: {'Date': 'Tue, 05 Aug 2025 02:56:19 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '26bda145536f48aca0c683ff11534732', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m

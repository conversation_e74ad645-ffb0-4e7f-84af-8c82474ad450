INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s', '50.1s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions (27).txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m🔄 断点续传: 从第320个任务开始[0m
INFO - [32m⚙️ 配置: 8Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions (27).txt[0m
INFO - [32m✅ 成功加载 19940 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: group_neutralize(cashflow_dividends/cap, subindust...[0m
INFO - [32m  2. 2: group_neutralize(cashflow_dividends/cap, market)[0m
INFO - [32m  3. 3: group_neutralize(cashflow_dividends/cap, industry)[0m
INFO - [32m  4. 4: group_neutralize(cashflow_dividends/cap, sector)[0m
INFO - [32m  5. 5: group_neutralize(cashflow_dividends/cap, country)[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m🔄 断点续传: 跳过前3200个Alpha，剩余16740个[0m
INFO - [32m✅ 创建1674个任务 (编号321-1994)[0m
INFO - [32m🚀 开始渐进式启动 8 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位7 等待 50.1 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.403358
    headers: {'Date': 'Tue, 05 Aug 2025 02:57:22 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '39dfd9a3735d4291a6447dcf124955a9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1674 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 2/8 | 待处理: 1672 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxrv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxrv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxrv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxrv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxrv/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxtl/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxtl/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxtl/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxtl/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pxtl/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.384710
    headers: {'Date': 'Tue, 05 Aug 2025 02:57:36 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'c06bd1197d5d44179b16472d5d689e63', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位1] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fb560>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.268862
    headers: {'Date': 'Tue, 05 Aug 2025 02:57:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '3', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.371755
    headers: {'Date': 'Tue, 05 Aug 2025 02:57:59 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8e083e9051ff4a88914bb6908c868d4d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔧 槽位4 启动[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.477733
    headers: {'Date': 'Tue, 05 Aug 2025 02:58:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e81d191eb45a46ffa239b4331b571c3c', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位2] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位7 启动[0m
INFO - [32m🔧 槽位6 启动[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.311061
    headers: {'Date': 'Tue, 05 Aug 2025 02:58:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '7d2c9959672b47fd8d4268f890a3505e', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位7] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x145957fe0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.255834
    headers: {'Date': 'Tue, 05 Aug 2025 02:58:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '13', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x145957fe0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.287111
    headers: {'Date': 'Tue, 05 Aug 2025 02:58:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '4', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.281151
    headers: {'Date': 'Tue, 05 Aug 2025 02:58:59 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '4ad5c897255144269d57f81b1bedff74', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31m❌ 创建WQB会话异常: WQB认证失败[0m
ERROR - [31m[槽位7] 自动重登录失败[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.361320
    headers: {'Date': 'Tue, 05 Aug 2025 02:59:07 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '46af6ecb853b4e5bb7394009eb9ecaaa', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.346347
    headers: {'Date': 'Tue, 05 Aug 2025 02:59:23 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '36588865c4a7481bb90cca4cec98371b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位1] 任务322重试成功提交[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x145955100>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.319207
    headers: {'Date': 'Tue, 05 Aug 2025 02:59:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'Retry-After': '13', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x145955100>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.294104
    headers: {'Date': 'Tue, 05 Aug 2025 02:59:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'Retry-After': '3', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.474765
    headers: {'Date': 'Tue, 05 Aug 2025 02:59:50 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '121add470b934e8baf2ea1b3b0022c0e', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtdv/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rtes/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.361673
    headers: {'Date': 'Tue, 05 Aug 2025 03:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'a4af7ea188f54b1ebab592e85c80ab31', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位7] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.417339
    headers: {'Date': 'Tue, 05 Aug 2025 03:00:26 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'a4f3f1592c544ddfa6f207764bebbe3d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位2] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14597f530>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.255187
    headers: {'Date': 'Tue, 05 Aug 2025 03:00:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '11', 'Retry-After': '11', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14597f530>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.247471
    headers: {'Date': 'Tue, 05 Aug 2025 03:00:58 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '2', 'Retry-After': '2', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ptcs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_pwia/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.503048
    headers: {'Date': 'Tue, 05 Aug 2025 03:00:51 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '886aa96c60264c6db2e16705f298d0f9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位0] 任务321 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1674 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1666 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.427177
    headers: {'Date': 'Tue, 05 Aug 2025 03:01:08 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'd7d358dbb7d546e292045f6c341e8d83', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.365453
    headers: {'Date': 'Tue, 05 Aug 2025 03:01:20 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'dfae8da89d4a4006a4f0608916446d84', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14597edb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.282891
    headers: {'Date': 'Tue, 05 Aug 2025 03:01:39 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '21', 'Retry-After': '21', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14597edb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.281727
    headers: {'Date': 'Tue, 05 Aug 2025 03:01:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '11', 'Retry-After': '11', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rcaa/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rdlf/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.387253
    headers: {'Date': 'Tue, 05 Aug 2025 03:01:42 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '817f8faf01f74615a1413370fed2ebea', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位3] 任务323 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1674 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1665 | 失败: 2[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位7] 任务327重试成功提交[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_repnotickermap/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rltr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.406286
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:07 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8a68e7a0a77e452880554c873d9fd507', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位2] 任务324 达到最大重试次数，请求仍返回空响应或429[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.372780
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:15 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'fa721335196e413494676baed80fc02f', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位5] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_stnr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_stnr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_stnr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_stnr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_stnr/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_swds/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_swds/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_swds/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_swds/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_swds/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.319508
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:27 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '599541cf64194f43b9ec4abf1f7663cc', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位2] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fa000>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.271173
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:38 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '22', 'Retry-After': '22', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fa000>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.275073
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'Retry-After': '13', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fa000>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.278445
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'Retry-After': '4', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.278497
    headers: {'Date': 'Tue, 05 Aug 2025 03:02:49 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '94b3e611bfac41f3ba8931ad06a8a229', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31m❌ 创建WQB会话异常: WQB认证失败[0m
ERROR - [31m[槽位2] 自动重登录失败[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_saoa/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sebv/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.485681
    headers: {'Date': 'Tue, 05 Aug 2025 03:03:09 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '81202e5a9b72403c81b0cfe4f51081f0', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fa600>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.280773
    headers: {'Date': 'Tue, 05 Aug 2025 03:03:27 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '33', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '33', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_rucs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sabs/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.404608
    headers: {'Date': 'Tue, 05 Aug 2025 03:03:29 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'f6f20e6fc8a14b4db7609376c7c08688', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位6] 任务328 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 2/1674 (0.1%) | 成功率: 33.3%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1662 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fa600>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.280862
    headers: {'Date': 'Tue, 05 Aug 2025 03:03:48 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '12', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '12', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.347389
    headers: {'Date': 'Tue, 05 Aug 2025 03:03:50 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'c21cdc4ca47845a2ba19006c68e5c75d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位2] 任务332重试成功提交[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.386375
    headers: {'Date': 'Tue, 05 Aug 2025 03:04:09 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e1c838df8d524944845140752ded9803', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位5] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位0] 任务329重试成功提交[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 4/1674 (0.2%) | 成功率: 50.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1661 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tibs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tibs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tibs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tibs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tibs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tnia/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tnia/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tnia/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tnia/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tnia/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.401987
    headers: {'Date': 'Tue, 05 Aug 2025 03:04:55 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '4eb2bd7426a844ee95f2e1578a2de0a6', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 8/1674 (0.5%) | 成功率: 66.7%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1659 | 失败: 4[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 14:45:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度330个任务 (起始320 + 会话10)[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.430428
    headers: {'Date': 'Tue, 05 Aug 2025 03:05:24 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '809679cff0dc4d7680babd612970ec82', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_smcs/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_sonm/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.334212
    headers: {'Date': 'Tue, 05 Aug 2025 03:05:36 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '2354e08808634ee7805af7a11432c3f8', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位5] 任务331 达到最大重试次数，请求仍返回空响应或429[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458f9fd0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.258241
    headers: {'Date': 'Tue, 05 Aug 2025 03:05:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '7', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ttiv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ttiv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ttiv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ttiv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_ttiv/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uaie/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uaie/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uaie/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uaie/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uaie/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.357262
    headers: {'Date': 'Tue, 05 Aug 2025 03:05:56 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '81039a9e7f06403abbfd53b442e4a6de', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位5] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位4] 任务335重试成功提交[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_tait/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_taiv/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.358373
    headers: {'Date': 'Tue, 05 Aug 2025 03:06:38 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '7beee0d7b751401f83c0708c44c60564', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
ERROR - [31m[槽位6] 任务333 达到最大重试次数，请求仍返回空响应或429[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 10/1674 (0.6%) | 成功率: 62.5%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1656 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 13:23:08[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uatv/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uatv/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uatv/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uatv/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_uatv/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vacl/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vacl/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vacl/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vacl/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vacl/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.387448
    headers: {'Date': 'Tue, 05 Aug 2025 03:06:51 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '6998eb66ca3541559bdfd05945a9e3f8', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位5] 任务338重试成功提交[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 12/1674 (0.7%) | 成功率: 66.7%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1655 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 10:31:11[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位6] 任务339重试成功提交[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 14/1674 (0.8%) | 成功率: 70.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1654 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 08:50:01[0m
INFO - [32m============================================================[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnin/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnin/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnin/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnin/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnin/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnis/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnis/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnis/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnis/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd23_vnis/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.361210
    headers: {'Date': 'Tue, 05 Aug 2025 03:08:19 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '585ff1ea29414a1e8234a7d8ee4680da', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位0] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m✅ [槽位0] 任务341重试成功提交[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 18/1674 (1.1%) | 成功率: 75.0%[0m
INFO - [32m⚡ 唯一效率: 1.5 唯一Alpha/分钟 | 唯一提取: 20/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1652 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 07:16:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度340个任务 (起始320 + 会话20)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 22/1674 (1.3%) | 成功率: 78.6%[0m
INFO - [32m⚡ 唯一效率: 2.2 唯一Alpha/分钟 | 唯一提取: 30/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1650 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 04:22:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 28/1674 (1.7%) | 成功率: 82.4%[0m
INFO - [32m⚡ 唯一效率: 2.0 唯一Alpha/分钟 | 唯一提取: 30/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1647 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 01:35:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度350个任务 (起始320 + 会话30)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 30/1674 (1.8%) | 成功率: 83.3%[0m
INFO - [32m⚡ 唯一效率: 2.0 唯一Alpha/分钟 | 唯一提取: 30/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1646 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 01:12:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 34/1674 (2.0%) | 成功率: 85.0%[0m
INFO - [32m⚡ 唯一效率: 1.9 唯一Alpha/分钟 | 唯一提取: 30/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1644 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 00:10:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 36/1674 (2.2%) | 成功率: 85.7%[0m
INFO - [32m⚡ 唯一效率: 2.3 唯一Alpha/分钟 | 唯一提取: 40/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1643 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 00:29:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度360个任务 (起始320 + 会话40)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 40/1674 (2.4%) | 成功率: 87.0%[0m
INFO - [32m⚡ 唯一效率: 2.2 唯一Alpha/分钟 | 唯一提取: 40/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1641 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 23:48:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 42/1674 (2.5%) | 成功率: 87.5%[0m
INFO - [32m⚡ 唯一效率: 2.6 唯一Alpha/分钟 | 唯一提取: 50/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1640 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 23:55:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度370个任务 (起始320 + 会话50)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 50/1674 (3.0%) | 成功率: 89.3%[0m
INFO - [32m⚡ 唯一效率: 4.0 唯一Alpha/分钟 | 唯一提取: 80/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1636 | 失败: 6[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 22:11:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 52/1674 (3.1%) | 成功率: 89.7%[0m
INFO - [32m⚡ 唯一效率: 4.1 唯一Alpha/分钟 | 唯一提取: 90/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1635 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 22:45:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 56/1674 (3.3%) | 成功率: 90.3%[0m
INFO - [32m⚡ 唯一效率: 4.8 唯一Alpha/分钟 | 唯一提取: 110/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1633 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 22:32:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 58/1674 (3.5%) | 成功率: 90.6%[0m
INFO - [32m⚡ 唯一效率: 5.0 唯一Alpha/分钟 | 唯一提取: 120/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1632 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 22:30:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度380个任务 (起始320 + 会话60)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 66/1674 (3.9%) | 成功率: 91.7%[0m
INFO - [32m⚡ 唯一效率: 6.6 唯一Alpha/分钟 | 唯一提取: 160/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1628 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 68/1674 (4.1%) | 成功率: 91.9%[0m
INFO - [32m⚡ 唯一效率: 6.7 唯一Alpha/分钟 | 唯一提取: 170/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1627 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:30:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度390个任务 (起始320 + 会话70)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 72/1674 (4.3%) | 成功率: 92.3%[0m
INFO - [32m⚡ 唯一效率: 6.9 唯一Alpha/分钟 | 唯一提取: 190/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1625 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:44:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 76/1674 (4.5%) | 成功率: 92.7%[0m
INFO - [32m⚡ 唯一效率: 7.5 唯一Alpha/分钟 | 唯一提取: 210/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1623 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度400个任务 (起始320 + 会话80)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 80/1674 (4.8%) | 成功率: 93.0%[0m
INFO - [32m⚡ 唯一效率: 7.9 唯一Alpha/分钟 | 唯一提取: 230/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1621 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 88/1674 (5.3%) | 成功率: 93.6%[0m
INFO - [32m⚡ 唯一效率: 8.8 唯一Alpha/分钟 | 唯一提取: 270/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1617 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:53:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度410个任务 (起始320 + 会话90)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 90/1674 (5.4%) | 成功率: 93.8%[0m
INFO - [32m⚡ 唯一效率: 8.8 唯一Alpha/分钟 | 唯一提取: 280/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1616 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:57:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 92/1674 (5.5%) | 成功率: 93.9%[0m
INFO - [32m⚡ 唯一效率: 8.9 唯一Alpha/分钟 | 唯一提取: 290/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1615 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:03:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度420个任务 (起始320 + 会话100)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 100/1674 (6.0%) | 成功率: 94.3%[0m
INFO - [32m⚡ 唯一效率: 9.9 唯一Alpha/分钟 | 唯一提取: 330/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1611 | 失败: 6[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:26:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 102/1674 (6.1%) | 成功率: 94.4%[0m
INFO - [32m⚡ 唯一效率: 9.9 唯一Alpha/分钟 | 唯一提取: 340/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1610 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:34:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 106/1674 (6.3%) | 成功率: 94.6%[0m
INFO - [32m⚡ 唯一效率: 10.2 唯一Alpha/分钟 | 唯一提取: 360/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1608 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:25:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 108/1674 (6.5%) | 成功率: 94.7%[0m
INFO - [32m⚡ 唯一效率: 10.3 唯一Alpha/分钟 | 唯一提取: 370/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1607 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:29:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度430个任务 (起始320 + 会话110)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 112/1674 (6.7%) | 成功率: 94.9%[0m
INFO - [32m⚡ 唯一效率: 10.5 唯一Alpha/分钟 | 唯一提取: 390/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1605 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:28:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 116/1674 (6.9%) | 成功率: 95.1%[0m
INFO - [32m⚡ 唯一效率: 10.8 唯一Alpha/分钟 | 唯一提取: 410/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1603 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:21:00[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度440个任务 (起始320 + 会话120)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 120/1674 (7.2%) | 成功率: 95.2%[0m
INFO - [32m⚡ 唯一效率: 11.0 唯一Alpha/分钟 | 唯一提取: 430/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1601 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:16:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 122/1674 (7.3%) | 成功率: 95.3%[0m
INFO - [32m⚡ 唯一效率: 11.1 唯一Alpha/分钟 | 唯一提取: 440/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1600 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:14:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 126/1674 (7.5%) | 成功率: 95.5%[0m
INFO - [32m⚡ 唯一效率: 11.3 唯一Alpha/分钟 | 唯一提取: 460/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1598 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:12:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 128/1674 (7.6%) | 成功率: 95.5%[0m
INFO - [32m⚡ 唯一效率: 11.3 唯一Alpha/分钟 | 唯一提取: 470/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1597 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:16:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度450个任务 (起始320 + 会话130)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 134/1674 (8.0%) | 成功率: 95.7%[0m
INFO - [32m⚡ 唯一效率: 11.6 唯一Alpha/分钟 | 唯一提取: 500/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1594 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:11:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度460个任务 (起始320 + 会话140)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 140/1674 (8.4%) | 成功率: 95.9%[0m
INFO - [32m⚡ 唯一效率: 11.9 唯一Alpha/分钟 | 唯一提取: 530/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1591 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:09:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 146/1674 (8.7%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 560/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1588 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:56:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度470个任务 (起始320 + 会话150)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 150/1674 (9.0%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 580/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1586 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:55:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 152/1674 (9.1%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 590/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1585 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:56:28[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 156/1674 (9.3%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 610/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1583 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:58:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度480个任务 (起始320 + 会话160)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 160/1674 (9.6%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 12.7 唯一Alpha/分钟 | 唯一提取: 630/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1581 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:58:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 166/1674 (9.9%) | 成功率: 96.5%[0m
INFO - [32m⚡ 唯一效率: 13.1 唯一Alpha/分钟 | 唯一提取: 660/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1578 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:46:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 168/1674 (10.0%) | 成功率: 96.6%[0m
INFO - [32m⚡ 唯一效率: 13.1 唯一Alpha/分钟 | 唯一提取: 670/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1577 | 失败: 6[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 19:47:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度490个任务 (起始320 + 会话170)[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fbb60>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.269138
    headers: {'Date': 'Tue, 05 Aug 2025 03:50:48 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '12', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '12', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fbb60>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.262098
    headers: {'Date': 'Tue, 05 Aug 2025 03:50:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'Retry-After': '3', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.256457
    headers: {'Date': 'Tue, 05 Aug 2025 03:50:59 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'd89c8ee2eff041f8ba9a2b79643f204e', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x13d702000>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.283226
    headers: {'Date': 'Tue, 05 Aug 2025 03:51:44 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '16', 'Retry-After': '16', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x13d702000>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.296712
    headers: {'Date': 'Tue, 05 Aug 2025 03:51:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'Retry-After': '7', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.292338
    headers: {'Date': 'Tue, 05 Aug 2025 03:51:55 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '597beb19ae5d4441905ea35f2c76a787', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14599dbb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.297887
    headers: {'Date': 'Tue, 05 Aug 2025 03:52:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '11', 'Retry-After': '11', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14599dbb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.275974
    headers: {'Date': 'Tue, 05 Aug 2025 03:52:58 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '2', 'Retry-After': '2', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.429961
    headers: {'Date': 'Tue, 05 Aug 2025 03:53:00 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '1bf74aaa5db94a308d8c3660c378d541', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x126493a40>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.227547
    headers: {'Date': 'Tue, 05 Aug 2025 03:53:54 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '6', 'Retry-After': '6', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b32c0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.304884
    headers: {'Date': 'Tue, 05 Aug 2025 03:54:51 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '9', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '9', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b4680>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.252145
    headers: {'Date': 'Tue, 05 Aug 2025 03:55:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '7', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
WARNING - [33m⏰ [槽位2] 任务413等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 170/1674 (10.2%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 11.5 唯一Alpha/分钟 | 唯一提取: 680/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1575 | 失败: 7[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:02:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
WARNING - [33m⏰ [槽位6] 任务412等待超时[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 181/1674 (10.8%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 730/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1569 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:32:19[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 184/1674 (11.0%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 12.0 唯一Alpha/分钟 | 唯一提取: 750/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1567 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:51:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 188/1674 (11.2%) | 成功率: 95.9%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 770/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1565 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:45:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度510个任务 (起始320 + 会话190)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 190/1674 (11.4%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 12.1 唯一Alpha/分钟 | 唯一提取: 780/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1564 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:51:08[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 194/1674 (11.6%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 800/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1562 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:47:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 198/1674 (11.8%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 820/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1560 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:46:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度520个任务 (起始320 + 会话200)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 200/1674 (11.9%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 830/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1559 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:50:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 202/1674 (12.1%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 12.3 唯一Alpha/分钟 | 唯一提取: 840/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1558 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:54:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 206/1674 (12.3%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 860/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1556 | 失败: 8[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:48:41[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位2] 任务420等待超时[0m
WARNING - [33m⏰ [槽位7] 任务421等待超时[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05502q/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05502q/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05502q/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05502q/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05502q/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05503/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05503/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05503/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05503/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(fnd28_value_05503/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.367095
    headers: {'Date': 'Tue, 05 Aug 2025 04:06:34 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'c4fcfa6ae4f24c5e9604ead1fbcc9a0a', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位2] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 208/1674 (12.4%) | 成功率: 95.4%[0m
INFO - [32m⚡ 唯一效率: 12.4 唯一Alpha/分钟 | 唯一提取: 870/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1553 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:51:14[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位2] 任务440重试成功提交[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度530个任务 (起始320 + 会话210)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 210/1674 (12.5%) | 成功率: 95.5%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 880/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1552 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:01:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 216/1674 (12.9%) | 成功率: 95.6%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 910/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1549 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:51:40[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 218/1674 (13.0%) | 成功率: 95.6%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 920/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1548 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:54:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度540个任务 (起始320 + 会话220)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 224/1674 (13.4%) | 成功率: 95.7%[0m
INFO - [32m⚡ 唯一效率: 12.8 唯一Alpha/分钟 | 唯一提取: 950/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1545 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:45:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 226/1674 (13.5%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 12.8 唯一Alpha/分钟 | 唯一提取: 960/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1544 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:45:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 228/1674 (13.6%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 12.8 唯一Alpha/分钟 | 唯一提取: 970/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1543 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:45:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度550个任务 (起始320 + 会话230)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 230/1674 (13.7%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 12.7 唯一Alpha/分钟 | 唯一提取: 980/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1542 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:51:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 234/1674 (14.0%) | 成功率: 95.9%[0m
INFO - [32m⚡ 唯一效率: 12.9 唯一Alpha/分钟 | 唯一提取: 1000/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1540 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:46:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 236/1674 (14.1%) | 成功率: 95.9%[0m
INFO - [32m⚡ 唯一效率: 12.9 唯一Alpha/分钟 | 唯一提取: 1010/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1539 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:46:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度560个任务 (起始320 + 会话240)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 242/1674 (14.5%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 13.2 唯一Alpha/分钟 | 唯一提取: 1040/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1536 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:36:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 244/1674 (14.6%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.2 唯一Alpha/分钟 | 唯一提取: 1050/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1535 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:38:10[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 246/1674 (14.7%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.1 唯一Alpha/分钟 | 唯一提取: 1060/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1534 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:43:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度570个任务 (起始320 + 会话250)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 250/1674 (14.9%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.1 唯一Alpha/分钟 | 唯一提取: 1080/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1532 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:45:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 258/1674 (15.4%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1120/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1528 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:32:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度580个任务 (起始320 + 会话260)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 260/1674 (15.5%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1130/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1527 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:34:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 262/1674 (15.7%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1140/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1526 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:34:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 264/1674 (15.8%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1150/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1525 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:34:59[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 266/1674 (15.9%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1160/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1524 | 失败: 10[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:37:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度590个任务 (起始320 + 会话270)[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14599d3d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.291709
    headers: {'Date': 'Tue, 05 Aug 2025 04:25:34 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '26', 'Retry-After': '26', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459fa600>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.271936
    headers: {'Date': 'Tue, 05 Aug 2025 04:26:51 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '9', 'Retry-After': '9', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459c6a50>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.299401
    headers: {'Date': 'Tue, 05 Aug 2025 04:27:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'Retry-After': '7', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1458fbec0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.278611
    headers: {'Date': 'Tue, 05 Aug 2025 04:28:44 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '16', 'Retry-After': '16', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b7680>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.308021
    headers: {'Date': 'Tue, 05 Aug 2025 04:29:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '7', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
WARNING - [33m⏰ [槽位2] 任务464等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 270/1674 (16.1%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 12.7 唯一Alpha/分钟 | 唯一提取: 1180/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1521 | 失败: 11[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:13:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14599fbf0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.272059
    headers: {'Date': 'Tue, 05 Aug 2025 04:30:35 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '25', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '25', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x13fa799a0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.263667
    headers: {'Date': 'Tue, 05 Aug 2025 04:30:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '3', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x101d83800>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.270941
    headers: {'Date': 'Tue, 05 Aug 2025 04:31:38 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '22', 'Retry-After': '22', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b4980>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.301924
    headers: {'Date': 'Tue, 05 Aug 2025 04:31:58 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '2', 'Retry-After': '2', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
WARNING - [33m⏰ [槽位1] 任务471等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 274/1674 (16.4%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1200/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1518 | 失败: 12[0m
INFO - [32m📡 API频率: 3/5 (60.0%) | ⚡ 部分并行: 2槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:08[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位0] 任务467等待超时[0m
WARNING - [33m⏰ [槽位3] 任务470等待超时[0m
WARNING - [33m⏰ [槽位4] 任务468等待超时[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 278/1674 (16.6%) | 成功率: 94.9%[0m
INFO - [32m⚡ 唯一效率: 12.2 唯一Alpha/分钟 | 唯一提取: 1220/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1513 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:40:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度600个任务 (起始320 + 会话280)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 284/1674 (17.0%) | 成功率: 95.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1250/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1510 | 失败: 15[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:31:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 288/1674 (17.2%) | 成功率: 95.0%[0m
INFO - [32m⚡ 唯一效率: 12.5 唯一Alpha/分钟 | 唯一提取: 1270/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1508 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:30:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度610个任务 (起始320 + 会话290)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 292/1674 (17.4%) | 成功率: 95.1%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 1290/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1506 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 294/1674 (17.6%) | 成功率: 95.1%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 1300/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1505 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 296/1674 (17.7%) | 成功率: 95.2%[0m
INFO - [32m⚡ 唯一效率: 12.6 唯一Alpha/分钟 | 唯一提取: 1310/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1504 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度620个任务 (起始320 + 会话300)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 302/1674 (18.0%) | 成功率: 95.3%[0m
INFO - [32m⚡ 唯一效率: 12.8 唯一Alpha/分钟 | 唯一提取: 1340/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1501 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 304/1674 (18.2%) | 成功率: 95.3%[0m
INFO - [32m⚡ 唯一效率: 12.8 唯一Alpha/分钟 | 唯一提取: 1350/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1500 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 308/1674 (18.4%) | 成功率: 95.4%[0m
INFO - [32m⚡ 唯一效率: 12.9 唯一Alpha/分钟 | 唯一提取: 1370/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1498 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:19:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度630个任务 (起始320 + 会话310)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 312/1674 (18.6%) | 成功率: 95.4%[0m
INFO - [32m⚡ 唯一效率: 13.0 唯一Alpha/分钟 | 唯一提取: 1390/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1496 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 316/1674 (18.9%) | 成功率: 95.5%[0m
INFO - [32m⚡ 唯一效率: 13.0 唯一Alpha/分钟 | 唯一提取: 1410/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1494 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度640个任务 (起始320 + 会话320)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 320/1674 (19.1%) | 成功率: 95.5%[0m
INFO - [32m⚡ 唯一效率: 13.1 唯一Alpha/分钟 | 唯一提取: 1430/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1492 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 326/1674 (19.5%) | 成功率: 95.6%[0m
INFO - [32m⚡ 唯一效率: 13.2 唯一Alpha/分钟 | 唯一提取: 1460/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1489 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:11:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度650个任务 (起始320 + 会话330)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 330/1674 (19.7%) | 成功率: 95.7%[0m
INFO - [32m⚡ 唯一效率: 13.3 唯一Alpha/分钟 | 唯一提取: 1480/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1487 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:12:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 334/1674 (20.0%) | 成功率: 95.7%[0m
INFO - [32m⚡ 唯一效率: 13.3 唯一Alpha/分钟 | 唯一提取: 1500/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1485 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:10:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 338/1674 (20.2%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 13.4 唯一Alpha/分钟 | 唯一提取: 1520/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1483 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:07:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度660个任务 (起始320 + 会话340)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 344/1674 (20.5%) | 成功率: 95.8%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1550/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1480 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:06:41[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 348/1674 (20.8%) | 成功率: 95.9%[0m
INFO - [32m⚡ 唯一效率: 13.6 唯一Alpha/分钟 | 唯一提取: 1570/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1478 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:03:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度670个任务 (起始320 + 会话350)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 352/1674 (21.0%) | 成功率: 95.9%[0m
INFO - [32m⚡ 唯一效率: 13.6 唯一Alpha/分钟 | 唯一提取: 1590/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1476 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:02:44[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 356/1674 (21.3%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 13.6 唯一Alpha/分钟 | 唯一提取: 1610/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1474 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:05:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 358/1674 (21.4%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 13.6 唯一Alpha/分钟 | 唯一提取: 1620/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1473 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:05:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度680个任务 (起始320 + 会话360)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 362/1674 (21.6%) | 成功率: 96.0%[0m
INFO - [32m⚡ 唯一效率: 13.7 唯一Alpha/分钟 | 唯一提取: 1640/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1471 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:02:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 366/1674 (21.9%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.8 唯一Alpha/分钟 | 唯一提取: 1660/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1469 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:00:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度690个任务 (起始320 + 会话370)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 370/1674 (22.1%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 1680/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1467 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:57:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 372/1674 (22.2%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.8 唯一Alpha/分钟 | 唯一提取: 1690/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1466 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:02:56[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 376/1674 (22.5%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 1710/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1464 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:59:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度700个任务 (起始320 + 会话380)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 380/1674 (22.7%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 1730/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1462 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:57:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 382/1674 (22.8%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 1740/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1461 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:57:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 384/1674 (22.9%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 1750/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1460 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:02:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 388/1674 (23.2%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 1770/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1458 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:00:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度710个任务 (起始320 + 会话390)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 390/1674 (23.3%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 1780/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1457 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:02:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 396/1674 (23.7%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.1 唯一Alpha/分钟 | 唯一提取: 1810/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1454 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:56:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 398/1674 (23.8%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 1820/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1453 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:00:27[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度720个任务 (起始320 + 会话400)[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 404/1674 (24.1%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.1 唯一Alpha/分钟 | 唯一提取: 1850/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1450 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:57:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 406/1674 (24.3%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 1860/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1449 | 失败: 15[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 20:57:35[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x146124cb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.248674
    headers: {'Date': 'Tue, 05 Aug 2025 05:09:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'Retry-After': '4', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b1310>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.273935
    headers: {'Date': 'Tue, 05 Aug 2025 05:10:50 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '10', 'Retry-After': '10', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b1310>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: -1 day, 23:59:59.864444
    headers: {'Date': 'Tue, 05 Aug 2025 05:10:59 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '1', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '1', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.377809
    headers: {'Date': 'Tue, 05 Aug 2025 05:11:01 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'b82fe95a5bd24eeea2628155f640ac55', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: [0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
ERROR - [31m❌ WQB认证失败: 204[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x13d136d20>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.251566
    headers: {'Date': 'Tue, 05 Aug 2025 05:11:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '13', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x146126780>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.272495
    headers: {'Date': 'Tue, 05 Aug 2025 05:12:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'Retry-After': '3', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x14597d7f0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.278204
    headers: {'Date': 'Tue, 05 Aug 2025 05:13:58 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '2', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '2', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459bb740>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.273502
    headers: {'Date': 'Tue, 05 Aug 2025 05:14:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '3', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32mWQBSession已重登录[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
WARNING - [33m⏰ [槽位7] 任务541等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 408/1674 (24.4%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.5 唯一Alpha/分钟 | 唯一提取: 1870/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1447 | 失败: 16[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度730个任务 (起始320 + 会话410)[0m
WARNING - [33m⏰ [槽位3] 任务538等待超时[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 418/1674 (25.0%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.7 唯一Alpha/分钟 | 唯一提取: 1910/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1442 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:15:33[0m
INFO - [32m============================================================[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度740个任务 (起始320 + 会话420)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 422/1674 (25.2%) | 成功率: 96.1%[0m
INFO - [32m⚡ 唯一效率: 13.7 唯一Alpha/分钟 | 唯一提取: 1940/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1439 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 426/1674 (25.4%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.7 唯一Alpha/分钟 | 唯一提取: 1960/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1437 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度750个任务 (起始320 + 会话430)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 432/1674 (25.8%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.8 唯一Alpha/分钟 | 唯一提取: 1990/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1434 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 434/1674 (25.9%) | 成功率: 96.2%[0m
INFO - [32m⚡ 唯一效率: 13.8 唯一Alpha/分钟 | 唯一提取: 2000/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1433 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:19:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 438/1674 (26.2%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 2020/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1431 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度760个任务 (起始320 + 会话440)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 440/1674 (26.3%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 2030/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1430 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:19:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 444/1674 (26.5%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 2050/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1428 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:38[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 448/1674 (26.8%) | 成功率: 96.3%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 2070/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1426 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度770个任务 (起始320 + 会话450)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 450/1674 (26.9%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 13.9 唯一Alpha/分钟 | 唯一提取: 2080/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1425 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 454/1674 (27.1%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 2100/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1423 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 456/1674 (27.2%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 2110/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1422 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:36[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度780个任务 (起始320 + 会话460)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 460/1674 (27.5%) | 成功率: 96.4%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 2130/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1420 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 462/1674 (27.6%) | 成功率: 96.5%[0m
INFO - [32m⚡ 唯一效率: 14.1 唯一Alpha/分钟 | 唯一提取: 2140/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1419 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 464/1674 (27.7%) | 成功率: 96.5%[0m
INFO - [32m⚡ 唯一效率: 14.1 唯一Alpha/分钟 | 唯一提取: 2150/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1418 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 466/1674 (27.8%) | 成功率: 96.5%[0m
INFO - [32m⚡ 唯一效率: 14.1 唯一Alpha/分钟 | 唯一提取: 2160/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1417 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:18[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度790个任务 (起始320 + 会话470)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 472/1674 (28.2%) | 成功率: 96.5%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 2190/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1414 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:13:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 474/1674 (28.3%) | 成功率: 96.5%[0m
INFO - [32m⚡ 唯一效率: 14.0 唯一Alpha/分钟 | 唯一提取: 2200/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1413 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:20:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度800个任务 (起始320 + 会话480)[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 482/1674 (28.8%) | 成功率: 96.6%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 2240/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1409 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:14:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 486/1674 (29.0%) | 成功率: 96.6%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 2260/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1407 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:15:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 488/1674 (29.2%) | 成功率: 96.6%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 2270/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1406 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度810个任务 (起始320 + 会话490)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 490/1674 (29.3%) | 成功率: 96.6%[0m
INFO - [32m⚡ 唯一效率: 14.2 唯一Alpha/分钟 | 唯一提取: 2280/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1405 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 494/1674 (29.5%) | 成功率: 96.7%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 2300/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1403 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 498/1674 (29.7%) | 成功率: 96.7%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 2320/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1401 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:14:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度820个任务 (起始320 + 会话500)[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 502/1674 (30.0%) | 成功率: 96.7%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 2340/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1399 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:26[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 504/1674 (30.1%) | 成功率: 96.7%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 2350/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1398 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 506/1674 (30.2%) | 成功率: 96.7%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 2360/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1397 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度830个任务 (起始320 + 会话510)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 510/1674 (30.5%) | 成功率: 96.8%[0m
INFO - [32m⚡ 唯一效率: 14.3 唯一Alpha/分钟 | 唯一提取: 2380/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1395 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 514/1674 (30.7%) | 成功率: 96.8%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2400/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1393 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:15:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 516/1674 (30.8%) | 成功率: 96.8%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2410/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1392 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:15:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 518/1674 (30.9%) | 成功率: 96.8%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2420/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1391 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度840个任务 (起始320 + 会话520)[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 522/1674 (31.2%) | 成功率: 96.8%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2440/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1389 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:15:22[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 524/1674 (31.3%) | 成功率: 96.9%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2450/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1388 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 526/1674 (31.4%) | 成功率: 96.9%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2460/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1387 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 528/1674 (31.5%) | 成功率: 96.9%[0m
INFO - [32m⚡ 唯一效率: 14.4 唯一Alpha/分钟 | 唯一提取: 2470/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1386 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度850个任务 (起始320 + 会话530)[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 534/1674 (31.9%) | 成功率: 96.9%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 2500/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1383 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:03[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 536/1674 (32.0%) | 成功率: 96.9%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 2510/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1382 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:16:19[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 538/1674 (32.1%) | 成功率: 96.9%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 2520/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1381 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度860个任务 (起始320 + 会话540)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 542/1674 (32.4%) | 成功率: 97.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 2540/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1379 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:37[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 544/1674 (32.5%) | 成功率: 97.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 2550/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1378 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:45[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度870个任务 (起始320 + 会话550)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 550/1674 (32.9%) | 成功率: 97.0%[0m
INFO - [32m⚡ 唯一效率: 14.5 唯一Alpha/分钟 | 唯一提取: 2580/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1375 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 554/1674 (33.1%) | 成功率: 97.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2600/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1373 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 556/1674 (33.2%) | 成功率: 97.0%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2610/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1372 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度880个任务 (起始320 + 会话560)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 560/1674 (33.5%) | 成功率: 97.1%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2630/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1370 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:19:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 566/1674 (33.8%) | 成功率: 97.1%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2660/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1367 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:42[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度890个任务 (起始320 + 会话570)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 570/1674 (34.1%) | 成功率: 97.1%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2680/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1365 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:17:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 572/1674 (34.2%) | 成功率: 97.1%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2690/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1364 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 574/1674 (34.3%) | 成功率: 97.1%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2700/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1363 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 578/1674 (34.5%) | 成功率: 97.1%[0m
INFO - [32m⚡ 唯一效率: 14.6 唯一Alpha/分钟 | 唯一提取: 2720/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1361 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:21:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度900个任务 (起始320 + 会话580)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 584/1674 (34.9%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2750/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1358 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:20:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 586/1674 (35.0%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2760/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1357 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:21:31[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度910个任务 (起始320 + 会话590)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 590/1674 (35.2%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2780/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1355 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:20:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 592/1674 (35.4%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2790/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1354 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:21:23[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 596/1674 (35.6%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2810/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1352 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:19:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度920个任务 (起始320 + 会话600)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 600/1674 (35.8%) | 成功率: 97.2%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2830/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1350 | 失败: 17[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:01[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 602/1674 (36.0%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2840/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1349 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:18:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 604/1674 (36.1%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2850/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1348 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:26[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 608/1674 (36.3%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.7 唯一Alpha/分钟 | 唯一提取: 2870/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1346 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度930个任务 (起始320 + 会话610)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 612/1674 (36.6%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2890/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1344 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 614/1674 (36.7%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2900/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1343 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 618/1674 (36.9%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2920/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1341 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:05[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度940个任务 (起始320 + 会话620)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 622/1674 (37.2%) | 成功率: 97.3%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2940/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1339 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:46[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 626/1674 (37.4%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2960/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1337 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度950个任务 (起始320 + 会话630)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 630/1674 (37.6%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 2980/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1335 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 634/1674 (37.9%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.9 唯一Alpha/分钟 | 唯一提取: 3000/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1333 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:22:11[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 638/1674 (38.1%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.9 唯一Alpha/分钟 | 唯一提取: 3020/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1331 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:21:20[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度960个任务 (起始320 + 会话640)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 640/1674 (38.2%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 3030/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1330 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 642/1674 (38.4%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 3040/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1329 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 646/1674 (38.6%) | 成功率: 97.4%[0m
INFO - [32m⚡ 唯一效率: 14.9 唯一Alpha/分钟 | 唯一提取: 3060/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1327 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度970个任务 (起始320 + 会话650)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 650/1674 (38.8%) | 成功率: 97.5%[0m
INFO - [32m⚡ 唯一效率: 14.8 唯一Alpha/分钟 | 唯一提取: 3080/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1325 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:34[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 656/1674 (39.2%) | 成功率: 97.5%[0m
INFO - [32m⚡ 唯一效率: 14.9 唯一Alpha/分钟 | 唯一提取: 3110/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1322 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:29[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 658/1674 (39.3%) | 成功率: 97.5%[0m
INFO - [32m⚡ 唯一效率: 14.9 唯一Alpha/分钟 | 唯一提取: 3120/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1321 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度980个任务 (起始320 + 会话660)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 666/1674 (39.8%) | 成功率: 97.5%[0m
INFO - [32m⚡ 唯一效率: 14.9 唯一Alpha/分钟 | 唯一提取: 3160/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1317 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:16[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度990个任务 (起始320 + 会话670)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 670/1674 (40.0%) | 成功率: 97.5%[0m
INFO - [32m⚡ 唯一效率: 15.0 唯一Alpha/分钟 | 唯一提取: 3180/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1315 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:54[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 672/1674 (40.1%) | 成功率: 97.5%[0m
INFO - [32m⚡ 唯一效率: 15.0 唯一Alpha/分钟 | 唯一提取: 3190/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1314 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:58[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1000个任务 (起始320 + 会话680)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 680/1674 (40.6%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.1 唯一Alpha/分钟 | 唯一提取: 3230/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1310 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:51[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 682/1674 (40.7%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.0 唯一Alpha/分钟 | 唯一提取: 3240/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1309 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 684/1674 (40.9%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.0 唯一Alpha/分钟 | 唯一提取: 3250/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1308 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1010个任务 (起始320 + 会话690)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 690/1674 (41.2%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.1 唯一Alpha/分钟 | 唯一提取: 3280/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1305 | 失败: 17[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:23:49[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 692/1674 (41.3%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.1 唯一Alpha/分钟 | 唯一提取: 3290/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1304 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 698/1674 (41.7%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.1 唯一Alpha/分钟 | 唯一提取: 3320/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1301 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:24:52[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1020个任务 (起始320 + 会话700)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 700/1674 (41.8%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.2 唯一Alpha/分钟 | 唯一提取: 3330/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1300 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:06[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 704/1674 (42.1%) | 成功率: 97.6%[0m
INFO - [32m⚡ 唯一效率: 15.2 唯一Alpha/分钟 | 唯一提取: 3350/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1298 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:24:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 708/1674 (42.3%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.2 唯一Alpha/分钟 | 唯一提取: 3370/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1296 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:24:43[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1030个任务 (起始320 + 会话710)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 710/1674 (42.4%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.1 唯一Alpha/分钟 | 唯一提取: 3380/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1295 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 718/1674 (42.9%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3420/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1291 | 失败: 17[0m
INFO - [32m📡 API频率: 2/5 (40.0%) | 🚀 智能并行: 3槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:24:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1040个任务 (起始320 + 会话720)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 722/1674 (43.1%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3440/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1289 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:13[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 726/1674 (43.4%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.2 唯一Alpha/分钟 | 唯一提取: 3460/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1287 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:07[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 728/1674 (43.5%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.2 唯一Alpha/分钟 | 唯一提取: 3470/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1286 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:24[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1050个任务 (起始320 + 会话730)[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 734/1674 (43.8%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3500/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1283 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:12[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 738/1674 (44.1%) | 成功率: 97.7%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3520/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1281 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:47[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1060个任务 (起始320 + 会话740)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 742/1674 (44.3%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3540/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1279 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 744/1674 (44.4%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3550/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1278 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:30[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1070个任务 (起始320 + 会话750)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 750/1674 (44.8%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.3 唯一Alpha/分钟 | 唯一提取: 3580/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1275 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:14[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 756/1674 (45.2%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 3610/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1272 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:57[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 758/1674 (45.3%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 3620/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1271 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:17[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1080个任务 (起始320 + 会话760)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 760/1674 (45.4%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 3630/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1270 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:32[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 762/1674 (45.5%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 3640/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1269 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:55[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 768/1674 (45.9%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 3670/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1266 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:26[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1090个任务 (起始320 + 会话770)[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 772/1674 (46.1%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3690/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1264 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:26:39[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 776/1674 (46.4%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3710/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1262 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:25:50[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 778/1674 (46.5%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.4 唯一Alpha/分钟 | 唯一提取: 3720/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1261 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:29:25[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1100个任务 (起始320 + 会话780)[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 782/1674 (46.7%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3740/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1259 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:33[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 786/1674 (47.0%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3760/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1257 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:27:53[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 788/1674 (47.1%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3770/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1256 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:09[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1110个任务 (起始320 + 会话790)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 790/1674 (47.2%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3780/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1255 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:48[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位2] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位2] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 794/1674 (47.4%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3800/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1253 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:30:21[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位3] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位3] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位1] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位1] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位7] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位7] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1120个任务 (起始320 + 会话800)[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 800/1674 (47.8%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3830/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1250 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:29:02[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位5] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位5] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位0] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位0] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 804/1674 (48.0%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.6 唯一Alpha/分钟 | 唯一提取: 3850/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1248 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:28:15[0m
INFO - [32m============================================================[0m
INFO - [32m🔍 [槽位6] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位6] 成功提取10/10个Alpha ID[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 808/1674 (48.3%) | 成功率: 97.9%[0m
INFO - [32m⚡ 唯一效率: 15.5 唯一Alpha/分钟 | 唯一提取: 3870/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1246 | 失败: 17[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:30:28[0m
INFO - [32m============================================================[0m
ERROR - [31m❌ [槽位3] 任务743状态检查异常: HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Read timed out. (read timeout=60)[0m
INFO - [32mWQBSession已重登录[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
WARNING - [33m⏰ [槽位2] 任务741等待超时[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 808/1674 (48.3%) | 成功率: 97.8%[0m
INFO - [32m⚡ 唯一效率: 15.0 唯一Alpha/分钟 | 唯一提取: 3870/16740[0m
INFO - [32m🔧 槽位: 7/8 | 待处理: 1245 | 失败: 18[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 21:51:13[0m
INFO - [32m============================================================[0m
WARNING - [33m⏰ [槽位1] 任务744等待超时[0m
WARNING - [33m⏰ [槽位7] 任务745等待超时[0m
WARNING - [33m⏰ [槽位0] 任务747等待超时[0m
WARNING - [33m⏰ [槽位5] 任务746等待超时[0m
INFO - [32m🔍 [槽位4] 开始串行提取10个Alpha ID[0m
INFO - [32m✅ [槽位4] 成功提取10/10个Alpha ID[0m
INFO - [32m💾 断点进度已保存: 总进度1130个任务 (起始320 + 会话810)[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.854786
    headers: {'Date': 'Tue, 05 Aug 2025 07:14:50 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'aed0179890f04d83b63ffde623e45ff9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m⏰ [槽位3] 任务743等待超时[0m
WARNING - [33m⏰ [槽位6] 任务748等待超时[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.364392
    headers: {'Date': 'Tue, 05 Aug 2025 07:15:07 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '077187aa41d44c24893180211db4dc4b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:02.453637
    headers: {'Date': 'Tue, 05 Aug 2025 07:15:21 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '2101dd0b03e9434c9deace4211b7cbd8', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cashflow_op/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cogs/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.393745
    headers: {'Date': 'Tue, 05 Aug 2025 07:16:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'dbe3be4e8c4a475e966a98d23f923a87', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位4] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(cost_of_revenue/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(current_ratio/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.362257
    headers: {'Date': 'Tue, 05 Aug 2025 07:16:21 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e14b41d467b7492c85abdd1e42dad82f', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位3] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1459b65a0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.327186
    headers: {'Date': 'Tue, 05 Aug 2025 07:16:53 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '7', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '7', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}[0m
WARNING - [33m<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_zscore(debt_lt/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.743730
    headers: {'Date': 'Tue, 05 Aug 2025 07:16:46 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '52fb749aabde446791ca59698c1c6ad9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}[0m
WARNING - [33m[槽位6] 自动重登录WQBSession[0m
INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m✅ [槽位4] 任务755重试成功提交[0m
INFO - [32m✅ [槽位3] 任务756重试成功提交[0m
ERROR - [31m❌ [槽位0] 任务753状态检查异常: HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Read timed out. (read timeout=60)[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位5] 任务754状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31mWQBSession重登录失败[0m
ERROR - [31m❌ [槽位2] 任务750状态检查异常: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))[0m
INFO - [32mWQBSession已重登录[0m

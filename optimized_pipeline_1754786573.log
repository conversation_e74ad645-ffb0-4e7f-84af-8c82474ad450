# INFO 2025-08-10 08:42:57,180
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-10 08:43:37,472
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-10 08:43:58,619
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_zscore(power(ts_rank(abs(rsk70_mfm2_usfast_value),252),2)-power(ts_rank(rsk70_mfm2_usfast_value,252),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_zscore(power(ts_rank(abs(rsk70_mfm2_usfast_value),252),2)-power(ts_rank(rsk70_mfm2_usfast_value,252),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),5),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,5),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.321508
    headers: {'Date': 'Sun, 10 Aug 2025 00:43:56 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e7794c12d34043e4a98f2c7ade754def', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-10 08:44:02,548
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-10 08:44:10,879
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),20),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,20),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_1drevrsl),120),2)-power(ts_zscore(rsk70_mfm2_usfast_1drevrsl,120),2),industry)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.384340
    headers: {'Date': 'Sun, 10 Aug 2025 00:44:08 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '9b9e7d3de345404499a05b4f11733ff3', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-10 08:44:15,024
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-10 08:44:33,011
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),exchange)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),5),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,5),2),subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),20),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,20),2),country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),20),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,20),2),currency)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'group_scale(power(ts_zscore(abs(rsk70_mfm2_usfast_beta),20),2)-power(ts_zscore(rsk70_mfm2_usfast_beta,20),2),exchange)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.487572
    headers: {'Date': 'Sun, 10 Aug 2025 00:44:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '08135a6e36ef41628c3ceb79de63ae4c', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-10 08:44:43,431
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x1304e4e90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.304974
    headers: {'Date': 'Sun, 10 Aug 2025 00:44:41 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '19', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '19', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-10 08:44:46,477
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 


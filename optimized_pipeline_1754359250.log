# INFO 2025-08-05 10:00:53,782
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-05 10:00:59,257
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:51:01,364
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(income_tax_payable/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.320495
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:01 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '320a4bdd6a8d46e98876edbc8ec0041b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:51:05,327
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:51:15,859
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(interest_expense/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.332523
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:15 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8eec7c79ea024a7698066ff6c7e5e99c', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:51:19,550
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:51:27,839
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.332193
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:27 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '90ecfd24530d48c0872696d9ef78f6f6', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-05 10:51:37,792
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f18290>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.309572
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:37 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '23', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '23', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-05 10:51:46,974
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f18290>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.251525
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:46 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '14', 'Retry-After': '14', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-05 10:51:56,040
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f18290>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.253280
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'Retry-After': '4', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-05 10:51:56,041
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.262870
    headers: {'Date': 'Tue, 05 Aug 2025 02:51:49 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '510603b95b3348a0a75b5fe0c0621988', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: 

# INFO 2025-08-05 10:51:56,041
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:52:16,438
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.359679
    headers: {'Date': 'Tue, 05 Aug 2025 02:52:16 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8962d009691043a0bd1da8b5e8e502f9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:52:20,100
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:52:49,832
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.337614
    headers: {'Date': 'Tue, 05 Aug 2025 02:52:49 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'bd394fe6831d4ece9674b27b8f9c24e0', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-05 10:52:59,804
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f319a0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.266637
    headers: {'Date': 'Tue, 05 Aug 2025 02:52:59 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '1', 'Retry-After': '1', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-05 10:53:02,751
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:53:30,397
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(inventory_turnover/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.591297
    headers: {'Date': 'Tue, 05 Aug 2025 02:53:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'cb166c7d737244ee89fb23dcb2a60946', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-05 10:53:40,434
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_income/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(operating_margin/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.357831
    headers: {'Date': 'Tue, 05 Aug 2025 02:53:40 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '36c85e35575e44bcb0abe829efdefd19', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:53:46,514
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:54:16,669
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.352498
    headers: {'Date': 'Tue, 05 Aug 2025 02:54:16 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e952317f86ac44e2961172e796764659', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:54:20,403
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:55:05,956
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.352398
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'be9b199ecd0d48e0957bb26d1b5bfe75', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:55:09,848
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:55:30,898
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_cur_oth/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(liabilities_curr/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.373734
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '9c3b0d3c53134dcb89535dd0c42df6db', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-05 10:55:45,039
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(rad/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(receivable/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.416866
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:44 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'b4c1ebfec3854c1ba7c1961ec425a69d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-05 10:55:55,409
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x158f1b410>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.326966
    headers: {'Date': 'Tue, 05 Aug 2025 02:55:55 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '5', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '5', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-05 10:55:58,761
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-05 10:56:20,269
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(pretax_income/cap, country)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, subindustry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, market)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, industry)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, sector)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'ASI', 'universe': 'MINVOL1M', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'ON'}, 'regular': 'group_neutralize(quick_ratio/cap, country)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.585886
    headers: {'Date': 'Tue, 05 Aug 2025 02:56:19 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '26bda145536f48aca0c683ff11534732', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-05 10:56:24,202
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 


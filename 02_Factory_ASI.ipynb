import logging
import sys

# 创建根日志器（确保重置配置）
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 清除所有现有处理器（避免重复输出）
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

# 通用日志格式
log_format = '[%(asctime)s] %(levelname)s - %(message)s'
formatter = logging.Formatter(log_format)

# 创建文件处理器（UTF-8编码，追加模式）
file_handler = logging.FileHandler('02_Simulation_log.log', mode='a', encoding='utf-8')
file_handler.setFormatter(formatter)

# 创建控制台处理器（输出到stdout）
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)

# 添加处理器到日志器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 验证
logger.info('日志初始化完成，编码正常')
print('日志初始化完成（通过print输出）')

import configparser
config_parser = configparser.ConfigParser()
config_parser.read('config.ini', encoding='utf-8')
print('配置文件读取完成，共发现以下段落：', config_parser.sections())

from common_config import CommonConfig   # 导入配置类
config = CommonConfig.from_config(config_parser)  # 解析配置，返回实例
config.log_parameters()                            # 打印所有参数

from common_auth import BrainAuth
auth_service = BrainAuth()          # BrainAuth实例化
session = auth_service.login()       # 执行标准登录流程

from db_utils import setup_database
db_service = setup_database(config_parser)

from datetime import datetime
import pytz

# 获取当前北京时间对象
beijing_now = datetime.now(pytz.timezone('Asia/Shanghai'))
# 将时间对象格式化为字符串
beijing_start = beijing_now.strftime('%Y-%m-%d %H:%M:%S')

# 按照指定格式输出时间
print("回测开始前北京时间:", beijing_start)

# 获取当前美东时间对象
eastern_now = datetime.now(pytz.timezone('US/Eastern'))
# 将时间对象格式化为字符串
eastern_start = eastern_now.strftime('%Y-%m-%d %H:%M:%S')

# 按照指定格式输出时间
print("回测开始前美东时间:", eastern_start)

start_date_beijing = "2025-07-07 12:26:19"
end_date_beijing   = "2025-07-15 14:26:19"

# start_date_beijing = beijing_start
# end_date_beijing   = beijing_end

from machine_lib import * 

#fo_tracker = get_alphas("07-13", "07-15", 1.00, 0.70, "USA", 400, "track")
#fo_tracker = get_alphas("07-07", "07-15", 1.00, 0.70, "ASI", 400, "track")
fo_tracker = get_alphas(start_date_beijing, end_date_beijing, 1.00, 0.70, "ASI", 400, "track")

# 手动设置测试区 alpha 数量
# test_alpha_count = 9900  # 手动设置测试区 alpha 数量

# 初始化一个空列表用于存放过滤后的结果
filtered_fo_tracker = []

# 遍历原始列表中的每个元素
for item in fo_tracker:
    # 检查当前元素是否不包含 "trade_when"
    if "trade_when" not in item[1]:
        # 如果不包含，则将该元素添加到过滤后的列表中
        filtered_fo_tracker.append(item)

# 提取 alpha_id 列表
alpha_ids = []
for item in filtered_fo_tracker:
    alpha_ids.append(item[0])


# 输出原始列表的元素数量
print("原始列表及元素数量:", len(fo_tracker))
print(fo_tracker)

# 输出过滤掉的元素数量
print("\n过滤掉的元素数量:", len(fo_tracker) - len(filtered_fo_tracker))

# 输出过滤后的列表的元素数量
print("\n过滤后的列表及元素数量:", len(filtered_fo_tracker))
print(filtered_fo_tracker)

# 输出 alpha_id 列表及数量
print("\n过滤后的 alpha_id 列表及数量:",len(alpha_ids) )
print(alpha_ids)



print(len(filtered_fo_tracker))
print(filtered_fo_tracker)
#print(extracted_list)

# 手动设置测试区 alpha 数量
test_alpha_count =59202 # 手动设置测试区 alpha 数量
# 计算回测效率百分比
backtest_efficiency = (len(filtered_fo_tracker) / test_alpha_count) * 100
print(f"\n回测效率百分比: {backtest_efficiency:.2f}%")

# 再次初始化两个列表用于存放进一步分类后的结果
contains_group = []
not_contains_group = []

# 遍历过滤后的列表中的每个元素
for item in filtered_fo_tracker:
    # 检查当前子列表是否包含 "group_"
    if "group_" in item[1]:
        # 如果包含，则将该子列表添加到 contains_group 列表中
        contains_group.append(item)
    else:
        # 如果不包含，则将该子列表添加到 not_contains_group 列表中
        not_contains_group.append(item)

# 输出包含 "group_" 的列表的元素数量
print("\n包含 'group_' 的列表及元素数量:", len(contains_group))
print(contains_group)

# 输出不包含 "group_" 的列表的元素数量
print("\n不包含 'group_' 的列表及元素数量:", len(not_contains_group))
print(not_contains_group)

# 输出包含和不包含 "group_" 的列表的总数量
print("\n包含和不包含 'group_' 的列表的总数量:", len(contains_group) + len(not_contains_group))

import field_data_alpha_ASI_01
recommended_fields_1_N = field_data_alpha_ASI_01.recommended_fields
print(len(recommended_fields_1_N))

retained_num = 5  

import re
# 初始化字典来统计每个字段出现的次数
field_count_matched_list = {field: 0 for field in recommended_fields_1_N}

# 初始化列表来保存筛选后的结果
filtered_exp_matched_list = []

# 对 filtered_fo_tracker 进行处理
for item in filtered_fo_tracker:
    exp_value = item[1]  # 假设 exp 值在子列表的第二个位置
    for field in recommended_fields_1_N:
        if field in exp_value:
            filtered_exp_matched_list.append(item)
            field_count_matched_list[field] += 1
            break  # 如果找到一个匹配，就停止检查其他字段




# # 统计在 matched_list=filtered_fo_tracker 中出现的字段及其数量
present_fields_matched_list = [field for field in recommended_fields_1_N if field_count_matched_list[field] > 0]
present_field_count_matched_list = len(present_fields_matched_list)


# 输出在 filtered_fo_tracker 中出现的字段数量及列表
# print("\n在 matched_list 中出现的字段数量及列表：")
print(f"出现的字段数量: {present_field_count_matched_list}")
# print("出现的字段列表:")
# for field in present_fields_matched_list:
#     print(field)

# 输出每个字段在 matched_list 中出现的次数
# print("\n每个字段在 matched_list 中出现的次数：")
print(f"\nextracted_list 中所有字段出现次数的合计数: {sum(field_count_matched_list.values())}")
for field, count in field_count_matched_list.items():
    if count > 0:
        print(f"字段: {field}, 出现次数: {count}")
# print(f"\nmatched_list 中所有字段出现次数的合计数: {sum(field_count_matched_list.values())}")

# 按 'fitness' 值从大到小排序
# 将负的 fitness 值转换为正值
sorted_filtered_exp_matched_list = sorted(filtered_exp_matched_list, key=lambda x: abs(x[3]), reverse=True)

# 输出每个字段原始的'exp'值和筛选后的'exp'值
# print("\nmatched_list 中每个字段原始的'exp'值和筛选后的'exp'值：")

# 收集所有字段的收集 exp 和 decay 信息，格式为 (exp, decay)
all_original_exps = []
all_retained_exps = []

# 统计每个字段及其筛选后的信息
field_stats = {}


for field in recommended_fields_1_N:
    original_alpha_ids = []
    original_exps = []   # ✅ 修改为 (exp, decay)
    original_sharp_values = []
    original_fitness = []

    retained_alpha_ids_list = []
    retained_exps = []   # ✅ 修改为 (exp, decay)
    retained_sharp_values = []
    retained_fitness_list = []


    # 获取原始exp、alpha_id、sharp 和 fitness
    for item in filtered_fo_tracker:
        if re.search(r'\b' + re.escape(field) + r'\b', item[1]):
            original_alpha_ids.append(item[0])  # 假设 alpha_id 在子列表的第一个位置            
            #original_exps.append(item[1])
            original_exps.append((item[1], item[-1]))  # ✅ 修改：添加 decay
            original_sharp_values.append(item[2])  # 假设 sharp 值在子列表的第三个位置
            original_fitness.append(item[4])



    # 获取筛选后的 exp、alpha_id、sharp 和 fitness
    temp_filtered_list = []
    for item in sorted_filtered_exp_matched_list:
        if re.search(r'\b' + re.escape(field) + r'\b', item[1]):
            temp_filtered_list.append(item)
    temp_filtered_list.sort(key=lambda x: abs(x[4]), reverse=True)
    # 获取前 retained_num 个表达式
    retained_alpha_ids_list = [item[0] for item in temp_filtered_list[:retained_num]]
    #retained_exps = [item[1] for item in temp_filtered_list[:retained_num]]
    retained_exps = [(item[1], item[-1]) for item in temp_filtered_list[:retained_num]]  # ✅ 修改：保留 exp 和 decay
    retained_sharp_values = [item[2] for item in temp_filtered_list[:retained_num]]  # 获取筛选后的 sharp 值
    retained_fitness_list = [item[4] for item in temp_filtered_list[:retained_num]]

     # ✅ 收集当前字段的原始和保留 exp+decay
    all_original_exps.extend(original_exps)
    all_retained_exps.extend(retained_exps)
    
    # 统计字段信息
    field_stats[field] = {
        '出现次数': field_count_matched_list[field],
        '原始exp值数量': len(original_exps),
        '筛选后exp值数量': len(retained_exps),
        '筛选后exp值列表': retained_exps,       # ✅ 已经是 (exp, decay)
        '筛选后alpha_id列表': retained_alpha_ids_list,
        '筛选后sharp值列表': retained_sharp_values,
        '筛选后fitness列表': retained_fitness_list
    }


    # if original_exps or retained_exps:
    #     print(f"\n字段: {field}")
    #     print(f"原始'exp'值数量: {len(original_exps)}")
    #     print("原始'exp'值、alpha_id、sharp 及 fitness:")
    #     for exp, fitness, alpha_id, sharp in zip(original_exps, original_fitness, original_alpha_ids, original_sharp_values):
    #         print(f"alpha_id: {alpha_id}, exp: {exp}, fitness: {fitness}, sharp: {sharp}")


    #     print(f"\n筛选后的'exp'值数量: {len(retained_exps)}")
    #     print("筛选后的'exp'值、alpha_id、sharp 及 fitness:")
    #     for exp, fitness, alpha_id, sharp in zip(retained_exps, retained_fitness_list, retained_alpha_ids_list, retained_sharp_values):
    #         print(f"alpha_id: {alpha_id}, exp: {exp}, fitness: {fitness}, sharp: {sharp}")

# 输出所有原始的和保留的exp, decay
#print("\n所有原始的 exp 列表:")
print("\n所有原始的 exp 和 decay 列表:")
print(f"原始 exp 数量: {len(all_original_exps)}")
print(all_original_exps)

#print("\n所有保留的 exp 列表:")
print("\n所有保留的 exp 和 decay 列表:")
print(f"保留 exp 数量: {len(all_retained_exps)}")
print(all_retained_exps)

# 按 fitness 排序输出所有筛选后的字段信息
print("\n按 fitness 排序的所有筛选后字段信息：")
sorted_fields_by_fitness = sorted(
    [(field, stats) for field, stats in field_stats.items() if stats['出现次数'] > 0],
    key=lambda x: sum(abs(f) for f in x[1]['筛选后fitness列表']) / len(x[1]['筛选后fitness列表']) if x[1]['筛选后fitness列表'] else 0,
    reverse=True
)

for field, stats in sorted_fields_by_fitness:
    print(f"\n字段: {field}")
    print(f"出现次数: {stats['出现次数']}")
    print(f"原始exp值数量: {stats['原始exp值数量']}")
    print(f"筛选后exp值数量: {stats['筛选后exp值数量']}")
    #print("筛选后的exp值、alpha_id、sharp 及 fitness:")
    print("筛选后的 (exp, decay)、alpha_id、sharp 及 fitness:")
    for (exp, decay), fitness, alpha_id, sharp in zip(
        stats['筛选后exp值列表'],
        stats['筛选后fitness列表'],
        stats['筛选后alpha_id列表'],
        stats['筛选后sharp值列表']
    ):
        #print(f"alpha_id: {alpha_id},fitness: {     fitness     },exp: {     exp     },sharp: {  sharp}")
        print(f"alpha_id: {alpha_id}, fitness: {fitness}, (exp, decay): ({exp}, {decay}), sharp: {sharp}")

print(len(all_retained_exps))
all_retained_exps

from field_data_group_ASI_02 import asi_group_13
print(len(asi_group_13))

so_alpha_list = []
group_ops = ["group_neutralize", "group_rank", "group_zscore"]
#group_ops = ["group_neutralize", "group_rank", "group_zscore", "group_scale"]
for expr, decay in all_retained_exps:
    #for alpha in get_group_second_order_factory([expr], group_ops, "USA"):
    for alpha in get_group_second_order_factory([expr], group_ops, "ASI"):    
        so_alpha_list.append((alpha,decay))

print(len(so_alpha_list))
random.shuffle(so_alpha_list)  #打乱二阶表达式

#so_alpha_list

import csv
import ast

csv_file = "02_factory_alpha_list.csv"  # 拟保存文件名

# 写入CSV文件
with open(csv_file, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    writer.writerow(['alpha_expression', 'decay'])  # 写入表头
    
    for alpha, decay in so_alpha_list:
        # 将元组格式化为字符串并转义特殊字符
        writer.writerow([alpha, decay])

print(f"已成功写入CSV文件: {csv_file}")
print(len(so_alpha_list))


# import csv
# import ast

# so_alpha_list = []
# csv_file = "02_factory_alpha_list.csv"  # 拟读取文件名

# # 读取CSV文件
# with open(csv_file, 'r', encoding='utf-8') as f:
#     reader = csv.reader(f)
#     next(reader)  # 跳过表头
    
#     for row in reader:
#         # 直接读取：第一列为表达式字符串，第二列为整数
#         alpha_expr = row[0]
#         decay = int(row[1])
#         so_alpha_list.append((alpha_expr, decay))

# print(f"从CSV恢复的列表长度: {len(so_alpha_list)}")

from simulation_service import SimulationService
simulation_service = SimulationService(config, auth_service, db_service)   

from factory.alpha_service import AlphaService  # 创建 AlphaService 实例，生成 Alpha
alpha_service = AlphaService(config, auth_service, db_service)        
filtered_alphas = alpha_service.filter_new_alphas(so_alpha_list)

filtered_alphas

for progress in simulation_service.batch_simulate(filtered_alphas):
    print(f"仿真进度: {progress}%")

# 获取当前北京时间对象
beijing_now = datetime.now(pytz.timezone('Asia/Shanghai'))
# 将时间对象格式化为字符串
beijing_end = beijing_now.strftime('%Y-%m-%d %H:%M:%S')

# 按照指定格式输出时间
print("回测结束时北京时间:", beijing_end)

# 获取当前美东时间对象
eastern_now = datetime.now(pytz.timezone('US/Eastern'))
# 将时间对象格式化为字符串
eastern_end = eastern_now.strftime('%Y-%m-%d %H:%M:%S')

# 按照指定格式输出时间
print("回测结束时美东时间:", eastern_end)

start_date_beijing = beijing_start
end_date_beijing = beijing_end
print(f'start_date_beijing = "{start_date_beijing}"')
print(f'end_date_beijing   = "{end_date_beijing}"')
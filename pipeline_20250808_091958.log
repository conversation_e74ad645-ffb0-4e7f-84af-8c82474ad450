INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions (29).txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m⚙️ 配置: 7Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions (29).txt[0m
INFO - [32m✅ 成功加载 13248 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  2. 2: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  3. 3: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  4. 4: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m  5. 5: min(group_rank(fnd28_anlev_value_08201a, oth455_co...[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m✅ 创建1325个任务 (编号1-1325)[0m
INFO - [32m🚀 开始渐进式启动 7 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1325 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/13248[0m
INFO - [32m🔧 槽位: 1/7 | 待处理: 1324 | 失败: 0[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
ERROR - [31m❌ [槽位0] 任务1失败: {'children': ['1XN2za7Iy57daKH45WjIr9N', 'eo0JJ3tm4BP9eB9R4n1ANs', '2Rnv1gog4wg9xR1hixFIBz0', '1FPD9Wbs4XKbEWh9y5j8H3', '4vXmwI1q757Q9EOTCY68Qki', '2U9kFIeE24v19tjxfaLTvDR', '3qVl8Mdpj5bM9zd171Cf1Pyf', '2rQjvF5bF4ucantRVZ3Qq98', '9IsK8Pl4tOb3mfwuJctSZ', 'ZAi3Qh0F5dSbBLhUzT8eUM'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位0] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位0] 开始错误快速定位：https://api.worldquantbrain.com/simulations/P9EXgffL4jaboYPQnW34HO[0m
INFO - [32m🔍 [槽位0] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位0] 开始并发错误诊断，共10个child， 并发度 1[0m
INFO - [32m✅ [槽位0] 错误定位完成![0m
INFO - [32m📊 [槽位0] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位0] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位0] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位0] 开始智能错误恢复：任务1[0m
INFO - [32m🔍 [槽位0] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位0] Alpha 1: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 2: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 3: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 4: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 5: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 6: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 7: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 8: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 9: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
WARNING - [33m❌ [槽位0] Alpha 10: min(group_rank(fnd28_anlev_value_08201a, oth455_co... - 真正错误: ERROR - Attempted to use unknown variable "fnd28_anlev_value_08201a"[0m
INFO - [32m✅ [槽位0] 智能恢复处理完成: 好Alpha 0个, 坏Alpha 10个, 创建重试任务 0个[0m
INFO - [32m✨ [槽位0] 智能恢复完成，好的Alpha已加入重试队列[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1325 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/13248[0m
INFO - [32m🔧 槽位: 3/7 | 待处理: 1321 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m🚫 错误Alpha: 10个 (已保存至 error_alphas_20250808_091958.csv)[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位4 启动[0m
ERROR - [31m❌ [槽位1] 任务2失败: {'children': ['3IHfXPfjH4q3axPAMI7KpI6', 'XiPpa3b15fL9hR2S0wYgC1', 'yORxdcTL4ro9p3QZFcvUXo', '4ACega4JC4hUaPMBxuxbMMx', '2vsB5yfbi4ltbyAmUXzDqME', '3MRQ2Z2Tv55MbNSFQQvjJux', '4DHd5w3Wn4iraYIDWxZjbub', '4cuEiD2E14rhcoA1ei7k6WJl', '3k1LUe5I54Vjaz9IM8mO6Go', '3qAFjUh1X58HcwwEYP9k1lo'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位1] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位1] 开始错误快速定位：https://api.worldquantbrain.com/simulations/2qqMjBgCz4oWcEheSxANBHz[0m
INFO - [32m🔍 [槽位1] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位1] 开始并发错误诊断，共10个child， 并发度 1[0m
INFO - [32m✅ [槽位1] 错误定位完成![0m
INFO - [32m📊 [槽位1] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位1] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位1] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位1] 开始智能错误恢复：任务2[0m
